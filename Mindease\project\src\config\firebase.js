const admin = require('firebase-admin');
const { initializeApp } = require('firebase/app');
const { getFirestore, connectFirestoreEmulator } = require('firebase/firestore');

// Firebase configuration for MindEase App
const firebaseConfig = {
  apiKey: "demo-api-key",
  authDomain: "mindease-demo.firebaseapp.com",
  projectId: "mindease-demo",
  storageBucket: "mindease-demo.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:demo-app-id"
};

// Initialize Firebase Admin (for server-side operations)
let adminApp;
let db;

const initializeFirebase = async () => {
  console.log('🔥 Initializing Firebase for MindEase...');

  try {
    // For development, we'll use mock database to avoid Firebase setup complexity
    console.log('🧪 Using mock database for development...');

    // Skip Firebase initialization and use mock database
    await createMockDatabase();

    console.log('✅ Mock database initialized successfully!');
    console.log('🌐 Using in-memory mock database for development');
    console.log('🚀 Database setup completed - Ready for real-time operations!');

  } catch (error) {
    console.error('❌ Database initialization failed:', error.message);

    // Fallback to mock database
    console.log('🔄 Falling back to mock database...');
    await createMockDatabase();
  }
};

// Create initial data in Firestore
const createInitialData = async () => {
  console.log('📝 Creating initial data in Firestore...');

  try {
    const bcrypt = require('bcryptjs');

    // Create admin user
    const adminRef = db.collection('users').doc('admin_user');
    const adminDoc = await adminRef.get();
    
    if (!adminDoc.exists) {
      const hashedAdminPassword = await bcrypt.hash('admin123', 10);
      await adminRef.set({
        username: 'admin',
        email: '<EMAIL>',
        passwordHash: hashedAdminPassword,
        role: 'admin',
        isBlocked: false,
        isActive: true,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });
      console.log('👤 Created admin user: <EMAIL>');
    }

    // Create test user
    const userRef = db.collection('users').doc('test_user');
    const userDoc = await userRef.get();
    
    if (!userDoc.exists) {
      const hashedUserPassword = await bcrypt.hash('password123', 10);
      await userRef.set({
        username: 'hadyy',
        email: '<EMAIL>',
        passwordHash: hashedUserPassword,
        role: 'user',
        isBlocked: false,
        isActive: true,
        profile: {
          firstName: 'Hady',
          lastName: 'User',
          age: 25,
          gender: 'male',
        },
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });
      console.log('👤 Created test user: <EMAIL>');
    }

    // Create test therapist
    const therapistRef = db.collection('therapists').doc('test_therapist');
    const therapistDoc = await therapistRef.get();
    
    if (!therapistDoc.exists) {
      const hashedTherapistPassword = await bcrypt.hash('therapist123', 10);
      await therapistRef.set({
        name: 'Dr. Sarah Johnson',
        email: '<EMAIL>',
        passwordHash: hashedTherapistPassword,
        phone: '******-0123',
        specialty: 'Clinical Psychology',
        specialization: 'Anxiety & Depression',
        experience: 8,
        location: 'New York, NY',
        rating: 4.8,
        reviewCount: 127,
        hourlyRate: 120,
        bio: 'Dr. Sarah Johnson is a licensed clinical psychologist with over 8 years of experience helping individuals overcome anxiety, depression, and trauma.',
        education: 'PhD in Clinical Psychology, Harvard University',
        languages: ['English', 'Spanish'],
        approaches: ['Cognitive Behavioral Therapy', 'Mindfulness-Based Therapy', 'Trauma-Informed Care'],
        availability: 'Mon-Fri: 9AM-6PM',
        isApproved: true,
        isBlocked: false,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });
      console.log('👩‍⚕️ Created test therapist: <EMAIL>');
    }

    // Create sample mood entries
    const moodEntriesRef = db.collection('moodEntries');
    const moodQuery = await moodEntriesRef.where('userId', '==', 'test_user').limit(1).get();
    
    if (moodQuery.empty) {
      const sampleMoods = [
        { mood: 'happy', intensity: 8, note: 'Had a great day at work!', date: new Date(Date.now() - 86400000) },
        { mood: 'anxious', intensity: 6, note: 'Feeling worried about upcoming presentation', date: new Date(Date.now() - 172800000) },
        { mood: 'calm', intensity: 7, note: 'Meditation session helped a lot', date: new Date(Date.now() - 259200000) },
      ];

      for (const mood of sampleMoods) {
        await moodEntriesRef.add({
          userId: 'test_user',
          ...mood,
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
        });
      }
      console.log('😊 Created sample mood entries');
    }

    // Create sample reviews
    const reviewsRef = db.collection('reviews');
    const reviewQuery = await reviewsRef.where('therapistId', '==', 'test_therapist').limit(1).get();
    
    if (reviewQuery.empty) {
      const sampleReviews = [
        {
          therapistId: 'test_therapist',
          userId: 'test_user',
          userName: 'John D.',
          rating: 5,
          comment: 'Dr. Johnson has been incredibly helpful. Her approach is both professional and compassionate.',
          date: new Date('2024-01-15'),
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
        },
        {
          therapistId: 'test_therapist',
          userId: 'user_2',
          userName: 'Maria S.',
          rating: 5,
          comment: 'Excellent therapist! I\'ve seen significant improvement in my anxiety levels.',
          date: new Date('2024-01-10'),
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
        },
      ];

      for (const review of sampleReviews) {
        await reviewsRef.add(review);
      }
      console.log('⭐ Created sample reviews');
    }

    console.log('✅ Initial data creation completed!');

  } catch (error) {
    console.error('❌ Error creating initial data:', error);
  }
};

// Mock database fallback
let mockData = {
  users: [],
  therapists: [],
  appointments: [],
  moodEntries: [],
  journalEntries: [],
  testResults: [],
  reviews: []
};

const createMockDatabase = async () => {
  console.log('🔧 Setting up mock database...');
  
  const bcrypt = require('bcryptjs');
  
  // Create mock data (same as before)
  const hashedAdminPassword = await bcrypt.hash('admin123', 10);
  mockData.users.push({
    id: 'admin_user',
    username: 'admin',
    email: '<EMAIL>',
    passwordHash: hashedAdminPassword,
    role: 'admin',
    isBlocked: false,
    isActive: true,
    createdAt: new Date(),
  });

  const hashedUserPassword = await bcrypt.hash('password123', 10);
  mockData.users.push({
    id: 'test_user',
    username: 'hadyy',
    email: '<EMAIL>',
    passwordHash: hashedUserPassword,
    role: 'user',
    isBlocked: false,
    isActive: true,
    createdAt: new Date(),
  });

  console.log('✅ Mock database ready!');
};

// Database operation helpers
const getDb = () => {
  if (db) {
    return db;
  } else {
    // Return mock database interface
    return {
      collection: (name) => ({
        doc: (id) => ({
          get: async () => {
            const item = mockData[name]?.find(item => item.id === id);
            return {
              exists: !!item,
              data: () => item,
              id: id
            };
          },
          set: async (data) => {
            const index = mockData[name]?.findIndex(item => item.id === id);
            if (index >= 0) {
              mockData[name][index] = { ...data, id };
            } else {
              mockData[name] = mockData[name] || [];
              mockData[name].push({ ...data, id });
            }
          }
        }),
        add: async (data) => {
          const id = Date.now().toString();
          mockData[name] = mockData[name] || [];
          mockData[name].push({ ...data, id });
          return { id };
        },
        where: (field, op, value) => ({
          get: async () => {
            const items = mockData[name]?.filter(item => {
              if (op === '==') return item[field] === value;
              return false;
            }) || [];
            return {
              empty: items.length === 0,
              docs: items.map(item => ({
                id: item.id,
                data: () => item
              }))
            };
          }
        })
      })
    };
  }
};

// Graceful shutdown
process.on('SIGINT', async () => {
  if (adminApp) {
    await adminApp.delete();
    console.log('🛑 Firebase connection closed');
  } else {
    console.log('🛑 Mock database session ended');
  }
  process.exit(0);
});

module.exports = {
  initializeFirebase,
  getDb,
  admin,
  mockData
};
