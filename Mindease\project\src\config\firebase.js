const admin = require('firebase-admin');

// Real Firebase configuration for MindEase App
// Using Firebase's demo project for development
const firebaseConfig = {
  projectId: "demo-mindease-app",
  // For demo purposes, we'll use Firebase's demo project
  // In production, you would use your actual Firebase project credentials
};

// Initialize Firebase Admin (for server-side operations)
let adminApp;
let db;

const initializeFirebase = async () => {
  console.log('🔥 Initializing Real-time Firebase for MindEase...');

  try {
    // Initialize Firebase Admin with demo project for real-time functionality
    console.log('🌐 Setting up real-time Firebase database...');

    // Set emulator host BEFORE initializing Firebase
    process.env.FIRESTORE_EMULATOR_HOST = 'localhost:8080';
    process.env.GCLOUD_PROJECT = firebaseConfig.projectId;

    if (!admin.apps.length) {
      // Initialize with demo project - no credentials needed for emulator
      adminApp = admin.initializeApp({
        projectId: firebaseConfig.projectId,
      });
    } else {
      adminApp = admin.app();
    }

    // Initialize Firestore (will automatically use emulator)
    db = admin.firestore();

    console.log('✅ Real-time Firebase initialized successfully!');
    console.log('🌐 Using Firestore with real-time capabilities');
    console.log('📡 Database running on: localhost:8080');

    // Create initial data for testing
    await createInitialData();

    console.log('🚀 Real-time database setup completed!');
    console.log('💾 All data operations are now real-time and persistent');

  } catch (error) {
    console.error('❌ Firebase initialization failed:', error.message);
    console.log('🔄 Starting Firebase emulator automatically...');

    // Try to start Firebase emulator
    await startFirebaseEmulator();
  }
};

// Start Firebase emulator automatically
const startFirebaseEmulator = async () => {
  console.log('🚀 Starting Firebase emulator...');

  try {
    const { spawn } = require('child_process');

    // Start Firebase emulator
    const emulator = spawn('firebase', ['emulators:start', '--only', 'firestore'], {
      stdio: 'pipe',
      shell: true
    });

    emulator.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('Firestore Emulator running')) {
        console.log('✅ Firebase emulator started successfully!');
      }
    });

    emulator.stderr.on('data', (data) => {
      console.log('Firebase emulator:', data.toString());
    });

    // Wait a moment for emulator to start
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Try to initialize again
    await initializeFirebase();

  } catch (error) {
    console.log('⚠️ Could not start Firebase emulator automatically');
    console.log('💡 Please install Firebase CLI: npm install -g firebase-tools');
    console.log('💡 Then run: firebase emulators:start --only firestore');

    // Use simplified in-memory database as fallback
    await createSimpleDatabase();
  }
};

// Create simple in-memory database that mimics Firestore
const createSimpleDatabase = async () => {
  console.log('🔧 Creating simple real-time database...');

  // Create a simple database that works like Firestore
  const simpleDB = {
    collections: {},

    collection(name) {
      if (!this.collections[name]) {
        this.collections[name] = { docs: {} };
      }

      return {
        doc(id) {
          return {
            async get() {
              const doc = simpleDB.collections[name].docs[id];
              return {
                exists: !!doc,
                id: id,
                data: () => doc
              };
            },
            async set(data) {
              simpleDB.collections[name].docs[id] = {
                ...data,
                id: id,
                createdAt: new Date(),
                updatedAt: new Date()
              };
              return { id };
            },
            async update(data) {
              if (simpleDB.collections[name].docs[id]) {
                simpleDB.collections[name].docs[id] = {
                  ...simpleDB.collections[name].docs[id],
                  ...data,
                  updatedAt: new Date()
                };
              }
              return { id };
            },
            async delete() {
              delete simpleDB.collections[name].docs[id];
              return true;
            }
          };
        },

        async add(data) {
          const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
          await this.doc(id).set(data);
          return { id };
        },

        async get() {
          const docs = Object.values(simpleDB.collections[name].docs).map(doc => ({
            id: doc.id,
            data: () => doc,
            exists: true
          }));
          return { docs, empty: docs.length === 0 };
        },

        where(field, operator, value) {
          return {
            async get() {
              const allDocs = Object.values(simpleDB.collections[name].docs);
              const filteredDocs = allDocs.filter(doc => {
                if (operator === '==') return doc[field] === value;
                if (operator === '!=') return doc[field] !== value;
                if (operator === '>') return doc[field] > value;
                if (operator === '<') return doc[field] < value;
                if (operator === '>=') return doc[field] >= value;
                if (operator === '<=') return doc[field] <= value;
                return false;
              }).map(doc => ({
                id: doc.id,
                data: () => doc,
                exists: true
              }));

              return { docs: filteredDocs, empty: filteredDocs.length === 0 };
            },

            orderBy(field, direction = 'asc') {
              return {
                async get() {
                  const result = await this.get();
                  result.docs.sort((a, b) => {
                    const aVal = a.data()[field];
                    const bVal = b.data()[field];
                    if (direction === 'desc') {
                      return bVal > aVal ? 1 : -1;
                    }
                    return aVal > bVal ? 1 : -1;
                  });
                  return result;
                }
              };
            },

            limit(count) {
              return {
                async get() {
                  const result = await this.get();
                  result.docs = result.docs.slice(0, count);
                  return result;
                }
              };
            }
          };
        },

        orderBy(field, direction = 'asc') {
          return {
            async get() {
              const result = await this.get();
              result.docs.sort((a, b) => {
                const aVal = a.data()[field];
                const bVal = b.data()[field];
                if (direction === 'desc') {
                  return bVal > aVal ? 1 : -1;
                }
                return aVal > bVal ? 1 : -1;
              });
              return result;
            }
          };
        }
      };
    }
  };

  // Set the database
  db = simpleDB;

  console.log('✅ Simple real-time database created!');
  console.log('💾 All operations are now real-time and in-memory');

  // Create initial data
  await createInitialData();
};

// Create initial data in database
const createInitialData = async () => {
  console.log('📝 Creating initial data in Firestore...');

  try {
    const bcrypt = require('bcryptjs');

    // Create admin user
    const adminRef = db.collection('users').doc('admin_user');
    const adminDoc = await adminRef.get();
    
    if (!adminDoc.exists) {
      const hashedAdminPassword = await bcrypt.hash('admin123', 10);
      await adminRef.set({
        username: 'admin',
        email: '<EMAIL>',
        passwordHash: hashedAdminPassword,
        role: 'admin',
        isBlocked: false,
        isActive: true,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });
      console.log('👤 Created admin user: <EMAIL>');
    }

    // Create test user
    const userRef = db.collection('users').doc('test_user');
    const userDoc = await userRef.get();
    
    if (!userDoc.exists) {
      const hashedUserPassword = await bcrypt.hash('password123', 10);
      await userRef.set({
        username: 'hadyy',
        email: '<EMAIL>',
        passwordHash: hashedUserPassword,
        role: 'user',
        isBlocked: false,
        isActive: true,
        profile: {
          firstName: 'Hady',
          lastName: 'User',
          age: 25,
          gender: 'male',
        },
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });
      console.log('👤 Created test user: <EMAIL>');
    }

    // Create test therapist
    const therapistRef = db.collection('therapists').doc('test_therapist');
    const therapistDoc = await therapistRef.get();
    
    if (!therapistDoc.exists) {
      const hashedTherapistPassword = await bcrypt.hash('therapist123', 10);
      await therapistRef.set({
        name: 'Dr. Sarah Johnson',
        email: '<EMAIL>',
        passwordHash: hashedTherapistPassword,
        phone: '******-0123',
        specialty: 'Clinical Psychology',
        specialization: 'Anxiety & Depression',
        experience: 8,
        location: 'New York, NY',
        rating: 4.8,
        reviewCount: 127,
        hourlyRate: 120,
        bio: 'Dr. Sarah Johnson is a licensed clinical psychologist with over 8 years of experience helping individuals overcome anxiety, depression, and trauma.',
        education: 'PhD in Clinical Psychology, Harvard University',
        languages: ['English', 'Spanish'],
        approaches: ['Cognitive Behavioral Therapy', 'Mindfulness-Based Therapy', 'Trauma-Informed Care'],
        availability: 'Mon-Fri: 9AM-6PM',
        isApproved: true,
        isBlocked: false,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });
      console.log('👩‍⚕️ Created test therapist: <EMAIL>');
    }

    // Create sample mood entries
    const moodEntriesRef = db.collection('moodEntries');
    const moodQuery = await moodEntriesRef.where('userId', '==', 'test_user').limit(1).get();
    
    if (moodQuery.empty) {
      const sampleMoods = [
        { mood: 'happy', intensity: 8, note: 'Had a great day at work!', date: new Date(Date.now() - 86400000) },
        { mood: 'anxious', intensity: 6, note: 'Feeling worried about upcoming presentation', date: new Date(Date.now() - 172800000) },
        { mood: 'calm', intensity: 7, note: 'Meditation session helped a lot', date: new Date(Date.now() - 259200000) },
      ];

      for (const mood of sampleMoods) {
        await moodEntriesRef.add({
          userId: 'test_user',
          ...mood,
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
        });
      }
      console.log('😊 Created sample mood entries');
    }

    // Create sample reviews
    const reviewsRef = db.collection('reviews');
    const reviewQuery = await reviewsRef.where('therapistId', '==', 'test_therapist').limit(1).get();
    
    if (reviewQuery.empty) {
      const sampleReviews = [
        {
          therapistId: 'test_therapist',
          userId: 'test_user',
          userName: 'John D.',
          rating: 5,
          comment: 'Dr. Johnson has been incredibly helpful. Her approach is both professional and compassionate.',
          date: new Date('2024-01-15'),
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
        },
        {
          therapistId: 'test_therapist',
          userId: 'user_2',
          userName: 'Maria S.',
          rating: 5,
          comment: 'Excellent therapist! I\'ve seen significant improvement in my anxiety levels.',
          date: new Date('2024-01-10'),
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
        },
      ];

      for (const review of sampleReviews) {
        await reviewsRef.add(review);
      }
      console.log('⭐ Created sample reviews');
    }

    console.log('✅ Initial data creation completed!');

  } catch (error) {
    console.error('❌ Error creating initial data:', error);
  }
};

// Mock database fallback
let mockData = {
  users: [],
  therapists: [],
  appointments: [],
  moodEntries: [],
  journalEntries: [],
  testResults: [],
  reviews: []
};

const createMockDatabase = async () => {
  console.log('🔧 Setting up mock database...');
  
  const bcrypt = require('bcryptjs');
  
  // Create mock data (same as before)
  const hashedAdminPassword = await bcrypt.hash('admin123', 10);
  mockData.users.push({
    id: 'admin_user',
    username: 'admin',
    email: '<EMAIL>',
    passwordHash: hashedAdminPassword,
    role: 'admin',
    isBlocked: false,
    isActive: true,
    createdAt: new Date(),
  });

  const hashedUserPassword = await bcrypt.hash('password123', 10);
  mockData.users.push({
    id: 'test_user',
    username: 'hadyy',
    email: '<EMAIL>',
    passwordHash: hashedUserPassword,
    role: 'user',
    isBlocked: false,
    isActive: true,
    createdAt: new Date(),
  });

  console.log('✅ Mock database ready!');
};

// Database operation helpers
const getDb = () => {
  if (db) {
    return db;
  } else {
    // Return mock database interface
    return {
      collection: (name) => ({
        doc: (id) => ({
          get: async () => {
            const item = mockData[name]?.find(item => item.id === id);
            return {
              exists: !!item,
              data: () => item,
              id: id
            };
          },
          set: async (data) => {
            const index = mockData[name]?.findIndex(item => item.id === id);
            if (index >= 0) {
              mockData[name][index] = { ...data, id };
            } else {
              mockData[name] = mockData[name] || [];
              mockData[name].push({ ...data, id });
            }
          }
        }),
        add: async (data) => {
          const id = Date.now().toString();
          mockData[name] = mockData[name] || [];
          mockData[name].push({ ...data, id });
          return { id };
        },
        where: (field, op, value) => ({
          get: async () => {
            const items = mockData[name]?.filter(item => {
              if (op === '==') return item[field] === value;
              return false;
            }) || [];
            return {
              empty: items.length === 0,
              docs: items.map(item => ({
                id: item.id,
                data: () => item
              }))
            };
          }
        })
      })
    };
  }
};

// Graceful shutdown
process.on('SIGINT', async () => {
  if (adminApp) {
    await adminApp.delete();
    console.log('🛑 Firebase connection closed');
  } else {
    console.log('🛑 Mock database session ended');
  }
  process.exit(0);
});

module.exports = {
  initializeFirebase,
  getDb,
  admin,
  mockData
};
