import 'package:flutter/material.dart';
import 'package:myapp/theme/app_theme.dart';
import 'package:myapp/components/modern_button.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

class DepressionTestScreen extends StatefulWidget {
  final String userId;

  const DepressionTestScreen({
    super.key,
    required this.userId,
  });

  @override
  State<DepressionTestScreen> createState() => _DepressionTestScreenState();
}

class _DepressionTestScreenState extends State<DepressionTestScreen>
    with TickerProviderStateMixin {
  int currentQuestionIndex = 0;
  List<int> answers = [];
  bool isLoading = false;
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  final List<Map<String, dynamic>> questions = [
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by little interest or pleasure in doing things?',
      'options': [
        'Not at all',
        'Several days',
        'More than half the days',
        'Nearly every day'
      ]
    },
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by feeling down, depressed, or hopeless?',
      'options': [
        'Not at all',
        'Several days',
        'More than half the days',
        'Nearly every day'
      ]
    },
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by trouble falling or staying asleep, or sleeping too much?',
      'options': [
        'Not at all',
        'Several days',
        'More than half the days',
        'Nearly every day'
      ]
    },
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by feeling tired or having little energy?',
      'options': [
        'Not at all',
        'Several days',
        'More than half the days',
        'Nearly every day'
      ]
    },
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by poor appetite or overeating?',
      'options': [
        'Not at all',
        'Several days',
        'More than half the days',
        'Nearly every day'
      ]
    },
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by feeling bad about yourself or that you are a failure?',
      'options': [
        'Not at all',
        'Several days',
        'More than half the days',
        'Nearly every day'
      ]
    },
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by trouble concentrating on things?',
      'options': [
        'Not at all',
        'Several days',
        'More than half the days',
        'Nearly every day'
      ]
    },
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by moving or speaking slowly, or being fidgety?',
      'options': [
        'Not at all',
        'Several days',
        'More than half the days',
        'Nearly every day'
      ]
    },
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by thoughts that you would be better off dead?',
      'options': [
        'Not at all',
        'Several days',
        'More than half the days',
        'Nearly every day'
      ]
    },
  ];

  @override
  void initState() {
    super.initState();
    answers = List.filled(questions.length, -1);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _selectAnswer(int answerIndex) {
    setState(() {
      answers[currentQuestionIndex] = answerIndex;
    });
  }

  void _nextQuestion() {
    if (currentQuestionIndex < questions.length - 1) {
      _animationController.reset();
      setState(() {
        currentQuestionIndex++;
      });
      _animationController.forward();
    } else {
      _submitTest();
    }
  }

  void _previousQuestion() {
    if (currentQuestionIndex > 0) {
      _animationController.reset();
      setState(() {
        currentQuestionIndex--;
      });
      _animationController.forward();
    }
  }

  Future<void> _submitTest() async {
    setState(() {
      isLoading = true;
    });

    try {
      final totalScore = answers.reduce((a, b) => a + b);

      final response = await http.post(
        Uri.parse('http://localhost:3000/api/depression-test'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'userId': widget.userId,
          'answers': answers,
          'totalScore': totalScore,
          'testDate': DateTime.now().toIso8601String(),
        }),
      );

      if (response.statusCode == 201) {
        _showResults(totalScore);
      } else {
        throw Exception('Failed to submit test');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error submitting test: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  void _showResults(int totalScore) {
    String severity;
    String description;
    Color resultColor;

    if (totalScore <= 4) {
      severity = 'Minimal';
      description =
          'You show minimal signs of depression. Keep maintaining your mental wellness!';
      resultColor = AppTheme.successColor;
    } else if (totalScore <= 9) {
      severity = 'Mild';
      description =
          'You show mild signs of depression. Consider speaking with a mental health professional.';
      resultColor = AppTheme.warningColor;
    } else if (totalScore <= 14) {
      severity = 'Moderate';
      description =
          'You show moderate signs of depression. We recommend consulting with a therapist.';
      resultColor = Colors.orange;
    } else if (totalScore <= 19) {
      severity = 'Moderately Severe';
      description =
          'You show moderately severe signs of depression. Please consider professional help.';
      resultColor = Colors.deepOrange;
    } else {
      severity = 'Severe';
      description =
          'You show severe signs of depression. Please seek immediate professional help.';
      resultColor = AppTheme.errorColor;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.radiusL),
        ),
        title: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: resultColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusM),
              ),
              child: Icon(
                Icons.psychology,
                color: resultColor,
                size: 24,
              ),
            ),
            const SizedBox(width: AppTheme.spacingM),
            Expanded(
              child: Text(
                'Test Results',
                style: AppTheme.headingMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingM),
              decoration: BoxDecoration(
                color: resultColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusM),
              ),
              child: Row(
                children: [
                  Text(
                    'Severity: ',
                    style: AppTheme.bodyLarge.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    severity,
                    style: AppTheme.bodyLarge.copyWith(
                      color: resultColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: AppTheme.spacingL),
            Text(
              description,
              style: AppTheme.bodyMedium.copyWith(
                height: 1.5,
              ),
            ),
            const SizedBox(height: AppTheme.spacingL),
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingM),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusM),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: AppTheme.spacingS),
                  Expanded(
                    child: Text(
                      'This is a screening tool, not a diagnosis. Please consult a healthcare professional for proper evaluation.',
                      style: AppTheme.bodySmall.copyWith(
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to therapist list or booking
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Opening therapist recommendations...'),
                  backgroundColor: AppTheme.primaryColor,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
            ),
            child: const Text(
              'Find Help',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final progress = (currentQuestionIndex + 1) / questions.length;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Container(
        decoration: BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(progress),
              Expanded(
                child: isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _buildQuestionContent(),
              ),
              _buildNavigationButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(double progress) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingL),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.arrow_back_ios,
                    color: AppTheme.primaryColor),
              ),
              const SizedBox(width: AppTheme.spacingS),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Depression Assessment',
                      style: AppTheme.headingLarge.copyWith(
                        color: AppTheme.textPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'PHQ-9 Questionnaire',
                      style: AppTheme.bodyMedium.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingS),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusM),
                ),
                child: Icon(
                  Icons.psychology,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingL),
          // Progress Bar
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Question ${currentQuestionIndex + 1} of ${questions.length}',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.textSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    '${(progress * 100).round()}%',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppTheme.spacingS),
              LinearProgressIndicator(
                value: progress,
                backgroundColor: AppTheme.primaryColor.withOpacity(0.2),
                valueColor:
                    AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                minHeight: 6,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionContent() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(300 * _slideAnimation.value, 0),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: child,
          ),
        );
      },
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: AppTheme.spacingL),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppTheme.spacingL),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(AppTheme.radiusL),
                boxShadow: AppTheme.softShadow,
              ),
              child: Text(
                questions[currentQuestionIndex]['question'],
                style: AppTheme.headingMedium.copyWith(
                  color: AppTheme.textPrimary,
                  fontWeight: FontWeight.w600,
                  height: 1.4,
                ),
              ),
            ),
            const SizedBox(height: AppTheme.spacingXL),
            Expanded(
              child: ListView.builder(
                itemCount: questions[currentQuestionIndex]['options'].length,
                itemBuilder: (context, index) {
                  final isSelected = answers[currentQuestionIndex] == index;
                  return Container(
                    margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(AppTheme.radiusL),
                        onTap: () => _selectAnswer(index),
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          padding: const EdgeInsets.all(AppTheme.spacingL),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? AppTheme.primaryColor.withOpacity(0.1)
                                : Colors.white,
                            borderRadius:
                                BorderRadius.circular(AppTheme.radiusL),
                            border: Border.all(
                              color: isSelected
                                  ? AppTheme.primaryColor
                                  : Colors.grey.shade300,
                              width: isSelected ? 2 : 1,
                            ),
                            boxShadow: isSelected ? AppTheme.softShadow : null,
                          ),
                          child: Row(
                            children: [
                              Container(
                                width: 24,
                                height: 24,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: isSelected
                                        ? AppTheme.primaryColor
                                        : Colors.grey.shade400,
                                    width: 2,
                                  ),
                                  color: isSelected
                                      ? AppTheme.primaryColor
                                      : Colors.transparent,
                                ),
                                child: isSelected
                                    ? const Icon(
                                        Icons.check,
                                        size: 16,
                                        color: Colors.white,
                                      )
                                    : null,
                              ),
                              const SizedBox(width: AppTheme.spacingM),
                              Expanded(
                                child: Text(
                                  questions[currentQuestionIndex]['options']
                                      [index],
                                  style: AppTheme.bodyLarge.copyWith(
                                    color: isSelected
                                        ? AppTheme.primaryColor
                                        : AppTheme.textPrimary,
                                    fontWeight: isSelected
                                        ? FontWeight.w600
                                        : FontWeight.normal,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationButtons() {
    final canProceed = answers[currentQuestionIndex] != -1;
    final isLastQuestion = currentQuestionIndex == questions.length - 1;

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingL),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (currentQuestionIndex > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousQuestion,
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppTheme.primaryColor,
                  side: const BorderSide(color: AppTheme.primaryColor),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppTheme.radiusL),
                  ),
                  padding:
                      const EdgeInsets.symmetric(vertical: AppTheme.spacingM),
                ),
                child: const Text('Previous'),
              ),
            ),
          if (currentQuestionIndex > 0)
            const SizedBox(width: AppTheme.spacingM),
          Expanded(
            flex: currentQuestionIndex == 0 ? 1 : 1,
            child: ElevatedButton(
              onPressed: canProceed ? _nextQuestion : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                disabledBackgroundColor: Colors.grey.shade300,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.radiusL),
                ),
                padding:
                    const EdgeInsets.symmetric(vertical: AppTheme.spacingM),
              ),
              child: Text(
                isLastQuestion ? 'Submit Test' : 'Next',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
