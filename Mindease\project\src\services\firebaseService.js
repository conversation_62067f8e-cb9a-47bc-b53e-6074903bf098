const { getDb, admin } = require('../config/firebase');

class FirebaseService {
  constructor() {
    this.db = null;
    this.initialized = false;
  }

  async initialize() {
    if (!this.initialized) {
      this.db = getDb();
      this.initialized = true;
    }
  }

  // User operations
  async createUser(userData) {
    try {
      const userRef = this.db.collection('users').doc();
      const user = {
        ...userData,
        id: userRef.id,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      };
      await userRef.set(user);
      return { ...user, id: userRef.id };
    } catch (error) {
      throw new Error(`Error creating user: ${error.message}`);
    }
  }

  async getUserById(userId) {
    try {
      const userDoc = await this.db.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        return null;
      }
      return { id: userDoc.id, ...userDoc.data() };
    } catch (error) {
      throw new Error(`Error getting user: ${error.message}`);
    }
  }

  async getUserByEmail(email) {
    try {
      const userQuery = await this.db.collection('users').where('email', '==', email).get();
      if (userQuery.empty) {
        return null;
      }
      const userDoc = userQuery.docs[0];
      return { id: userDoc.id, ...userDoc.data() };
    } catch (error) {
      throw new Error(`Error getting user by email: ${error.message}`);
    }
  }

  async updateUser(userId, updateData) {
    try {
      const userRef = this.db.collection('users').doc(userId);
      await userRef.update({
        ...updateData,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });
      return await this.getUserById(userId);
    } catch (error) {
      throw new Error(`Error updating user: ${error.message}`);
    }
  }

  async deleteUser(userId) {
    try {
      await this.db.collection('users').doc(userId).delete();
      return true;
    } catch (error) {
      throw new Error(`Error deleting user: ${error.message}`);
    }
  }

  // Therapist operations
  async createTherapist(therapistData) {
    try {
      const therapistRef = this.db.collection('therapists').doc();
      const therapist = {
        ...therapistData,
        id: therapistRef.id,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      };
      await therapistRef.set(therapist);
      return { ...therapist, id: therapistRef.id };
    } catch (error) {
      throw new Error(`Error creating therapist: ${error.message}`);
    }
  }

  async getTherapistById(therapistId) {
    try {
      const therapistDoc = await this.db.collection('therapists').doc(therapistId).get();
      if (!therapistDoc.exists) {
        return null;
      }
      return { id: therapistDoc.id, ...therapistDoc.data() };
    } catch (error) {
      throw new Error(`Error getting therapist: ${error.message}`);
    }
  }

  async getTherapistByEmail(email) {
    try {
      const therapistQuery = await this.db.collection('therapists').where('email', '==', email).get();
      if (therapistQuery.empty) {
        return null;
      }
      const therapistDoc = therapistQuery.docs[0];
      return { id: therapistDoc.id, ...therapistDoc.data() };
    } catch (error) {
      throw new Error(`Error getting therapist by email: ${error.message}`);
    }
  }

  async getAllTherapists(filters = {}) {
    try {
      let query = this.db.collection('therapists');
      
      // Apply filters
      if (filters.isApproved !== undefined) {
        query = query.where('isApproved', '==', filters.isApproved);
      }
      if (filters.specialty) {
        query = query.where('specialty', '==', filters.specialty);
      }
      if (filters.location) {
        query = query.where('location', '==', filters.location);
      }

      const snapshot = await query.get();
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      throw new Error(`Error getting therapists: ${error.message}`);
    }
  }

  async updateTherapist(therapistId, updateData) {
    try {
      const therapistRef = this.db.collection('therapists').doc(therapistId);
      await therapistRef.update({
        ...updateData,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });
      return await this.getTherapistById(therapistId);
    } catch (error) {
      throw new Error(`Error updating therapist: ${error.message}`);
    }
  }

  // Appointment operations
  async createAppointment(appointmentData) {
    try {
      const appointmentRef = this.db.collection('appointments').doc();
      const appointment = {
        ...appointmentData,
        id: appointmentRef.id,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      };
      await appointmentRef.set(appointment);
      return { ...appointment, id: appointmentRef.id };
    } catch (error) {
      throw new Error(`Error creating appointment: ${error.message}`);
    }
  }

  async getAppointmentsByUserId(userId) {
    try {
      const appointmentsQuery = await this.db.collection('appointments')
        .where('userId', '==', userId)
        .orderBy('appointmentDate', 'desc')
        .get();
      
      return appointmentsQuery.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      throw new Error(`Error getting user appointments: ${error.message}`);
    }
  }

  async getAppointmentsByTherapistId(therapistId) {
    try {
      const appointmentsQuery = await this.db.collection('appointments')
        .where('therapistId', '==', therapistId)
        .orderBy('appointmentDate', 'desc')
        .get();
      
      return appointmentsQuery.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      throw new Error(`Error getting therapist appointments: ${error.message}`);
    }
  }

  async updateAppointment(appointmentId, updateData) {
    try {
      const appointmentRef = this.db.collection('appointments').doc(appointmentId);
      await appointmentRef.update({
        ...updateData,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });
      return await this.getAppointmentById(appointmentId);
    } catch (error) {
      throw new Error(`Error updating appointment: ${error.message}`);
    }
  }

  async getAppointmentById(appointmentId) {
    try {
      const appointmentDoc = await this.db.collection('appointments').doc(appointmentId).get();
      if (!appointmentDoc.exists) {
        return null;
      }
      return { id: appointmentDoc.id, ...appointmentDoc.data() };
    } catch (error) {
      throw new Error(`Error getting appointment: ${error.message}`);
    }
  }

  // Mood entry operations
  async createMoodEntry(moodData) {
    try {
      const moodRef = this.db.collection('moodEntries').doc();
      const moodEntry = {
        ...moodData,
        id: moodRef.id,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
      };
      await moodRef.set(moodEntry);
      return { ...moodEntry, id: moodRef.id };
    } catch (error) {
      throw new Error(`Error creating mood entry: ${error.message}`);
    }
  }

  async getMoodEntriesByUserId(userId) {
    try {
      const moodQuery = await this.db.collection('moodEntries')
        .where('userId', '==', userId)
        .orderBy('createdAt', 'desc')
        .get();
      
      return moodQuery.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      throw new Error(`Error getting mood entries: ${error.message}`);
    }
  }

  // Journal entry operations
  async createJournalEntry(journalData) {
    try {
      const journalRef = this.db.collection('journalEntries').doc();
      const journalEntry = {
        ...journalData,
        id: journalRef.id,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      };
      await journalRef.set(journalEntry);
      return { ...journalEntry, id: journalRef.id };
    } catch (error) {
      throw new Error(`Error creating journal entry: ${error.message}`);
    }
  }

  async getJournalEntriesByUserId(userId) {
    try {
      const journalQuery = await this.db.collection('journalEntries')
        .where('userId', '==', userId)
        .orderBy('createdAt', 'desc')
        .get();
      
      return journalQuery.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      throw new Error(`Error getting journal entries: ${error.message}`);
    }
  }

  // Test result operations
  async createTestResult(testData) {
    try {
      const testRef = this.db.collection('testResults').doc();
      const testResult = {
        ...testData,
        id: testRef.id,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
      };
      await testRef.set(testResult);
      return { ...testResult, id: testRef.id };
    } catch (error) {
      throw new Error(`Error creating test result: ${error.message}`);
    }
  }

  async getTestResultsByUserId(userId) {
    try {
      const testQuery = await this.db.collection('testResults')
        .where('userId', '==', userId)
        .orderBy('createdAt', 'desc')
        .get();
      
      return testQuery.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      throw new Error(`Error getting test results: ${error.message}`);
    }
  }

  // Review operations
  async createReview(reviewData) {
    try {
      const reviewRef = this.db.collection('reviews').doc();
      const review = {
        ...reviewData,
        id: reviewRef.id,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
      };
      await reviewRef.set(review);
      return { ...review, id: reviewRef.id };
    } catch (error) {
      throw new Error(`Error creating review: ${error.message}`);
    }
  }

  async getReviewsByTherapistId(therapistId) {
    try {
      const reviewQuery = await this.db.collection('reviews')
        .where('therapistId', '==', therapistId)
        .orderBy('createdAt', 'desc')
        .get();
      
      return reviewQuery.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      throw new Error(`Error getting reviews: ${error.message}`);
    }
  }
}

// Create singleton instance
const firebaseService = new FirebaseService();

module.exports = firebaseService;
