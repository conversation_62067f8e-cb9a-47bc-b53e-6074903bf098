import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:myapp/forget-password.dart';
import 'package:myapp/home_page.dart';
import 'package:myapp/signup_page.dart';
import 'package:myapp/therapist_dashboard.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:myapp/theme/app_theme.dart';
import 'package:myapp/components/modern_input.dart';
import 'package:myapp/components/modern_button.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  _LoginPageState createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool isPasswordVisible = false;
  String? selectedRole;

  @override
  void dispose() {
    emailController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  Future<void> login(BuildContext context) async {
    if (selectedRole == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Please select a role")),
      );
      return;
    }

    if (!_formKey.currentState!.validate()) {
      return;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    final email = emailController.text.trim();
    final password = passwordController.text.trim();

    try {
      final userType = selectedRole == "user" ? "users" : "therapists";

      // Add debug print to see what's being sent
      print(
          "Sending login request to: http://localhost:3000/api/${userType}/login");
      print("Request body: ${jsonEncode({
            "email": email,
            "password": password,
            "role": selectedRole,
          })}");

      final response = await http.post(
        Uri.parse("http://localhost:3000/api/${userType}/login"),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode({
          "email": email,
          "password": password,
          "role": selectedRole,
        }),
      );

      // Close the loading dialog
      Navigator.pop(context);

      // Add debug print to see the raw response
      print("Response status code: ${response.statusCode}");
      print("Response body: ${response.body}");

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        print("Decoded response data: $responseData");

        // Extract userId with null safety
        String? userId;

        // Try different possible response formats
        if (responseData is Map) {
          if (responseData.containsKey('user') && responseData['user'] is Map) {
            userId = responseData['user']['_id']?.toString();
          }
          if (userId == null && responseData.containsKey('userId')) {
            userId = responseData['userId']?.toString();
          }
          if (userId == null && responseData.containsKey('_id')) {
            userId = responseData['_id']?.toString();
          }
        }

        if (userId == null) {
          throw Exception("User ID not found in response: $responseData");
        }

        print("Extracted userId: $userId");

        SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.setString('userId', userId);

        // Navigate to the appropriate screen
        if (mounted) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => userType == 'users'
                  ? HomeScreen(userId: userId!)
                  : TherapistDashboard(therapistId: userId!),
            ),
          );
        }
      } else {
        final errorMsg = response.body.isNotEmpty
            ? jsonDecode(response.body)['message'] ?? "Unknown error"
            : "Server returned ${response.statusCode}";

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Login failed: $errorMsg")),
        );
      }
    } catch (error) {
      print("Login error: $error");
      // Make sure we're still mounted before showing a SnackBar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("An error occurred: $error")),
        );
      }
    }
  }

  String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return "Email is required";
    }
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return "Enter a valid email";
    }
    return null;
  }

  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return "Password is required";
    }
    if (value.length < 6) {
      return "Password must be at least 6 characters";
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppTheme.spacingL),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: AppTheme.spacingXL),
                  _header(),
                  const SizedBox(height: AppTheme.spacingXXL),
                  _inputField(),
                  const SizedBox(height: AppTheme.spacingL),
                  _roleSelector(),
                  const SizedBox(height: AppTheme.spacingL),
                  _forgotPasswordButton(),
                  const SizedBox(height: AppTheme.spacingM),
                  _loginButton(context),
                  const SizedBox(height: AppTheme.spacingXL),
                  _signup(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _header() {
    return Column(
      children: [
        // App Logo
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            borderRadius: BorderRadius.circular(25),
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryColor.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
                spreadRadius: 2,
              ),
            ],
          ),
          child: const Icon(
            Icons.psychology,
            size: 50,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: AppTheme.spacingL),
        // App Title
        Text(
          "MindEase",
          style: AppTheme.headingLarge.copyWith(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: AppTheme.spacingS),
        // Welcome Message
        Text(
          "Welcome Back!",
          style: AppTheme.headingMedium.copyWith(
            fontSize: 24,
            fontWeight: FontWeight.w600,
            color: AppTheme.primaryColor,
          ),
        ),
        const SizedBox(height: AppTheme.spacingS),
        Text(
          "Sign in to continue your wellness journey",
          style: AppTheme.bodyLarge.copyWith(
            color: AppTheme.textSecondary,
            fontWeight: FontWeight.w400,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _inputField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ModernInput(
          label: "Email Address",
          hint: "Enter your email",
          controller: emailController,
          prefixIcon: Icons.email_outlined,
          keyboardType: TextInputType.emailAddress,
          validator: validateEmail,
        ),
        const SizedBox(height: AppTheme.spacingM),
        ModernInput(
          label: "Password",
          hint: "Enter your password",
          controller: passwordController,
          prefixIcon: Icons.lock_outline,
          suffixIcon:
              isPasswordVisible ? Icons.visibility : Icons.visibility_off,
          onSuffixIconPressed: () {
            setState(() {
              isPasswordVisible = !isPasswordVisible;
            });
          },
          obscureText: !isPasswordVisible,
          validator: validatePassword,
        ),
      ],
    );
  }

  Widget _forgotPasswordButton() {
    return Align(
      alignment: Alignment.centerRight,
      child: TextButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => ForgotPasswordPage()),
          );
        },
        child: const Text(
          "Forgot Password?",
          style: TextStyle(
            color: AppTheme.primaryColor,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _loginButton(BuildContext context) {
    return PrimaryButton(
      text: "Sign In",
      onPressed: () async {
        await login(context);
      },
      icon: Icons.login,
    );
  }

  Widget _signup() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text(
          "Don't have an account? ",
          style: AppTheme.bodyMedium,
        ),
        TextButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => SignupPage()),
            );
          },
          child: const Text(
            "Sign Up",
            style: TextStyle(
              color: AppTheme.primaryColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _roleSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "I am a...",
          style: AppTheme.headingSmall,
        ),
        const SizedBox(height: AppTheme.spacingM),
        Row(
          children: [
            Expanded(
              child: _buildRoleCard(
                "Patient",
                "user",
                Icons.person_outline,
                "Seeking mental health support",
              ),
            ),
            const SizedBox(width: AppTheme.spacingM),
            Expanded(
              child: _buildRoleCard(
                "Therapist",
                "therapist",
                Icons.psychology_outlined,
                "Providing mental health services",
              ),
            ),
          ],
        ),
        if (selectedRole == null)
          const Padding(
            padding: EdgeInsets.only(top: AppTheme.spacingS),
            child: Text(
              "Please select a role",
              style: TextStyle(color: AppTheme.errorColor, fontSize: 12),
            ),
          ),
      ],
    );
  }

  Widget _buildRoleCard(
      String title, String value, IconData icon, String description) {
    final isSelected = selectedRole == value;
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedRole = value;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.primaryColor.withOpacity(0.1)
              : AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(AppTheme.radiusM),
          border: Border.all(
            color: isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected ? AppTheme.softShadow : null,
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: isSelected ? AppTheme.primaryColor : AppTheme.textLight,
            ),
            const SizedBox(height: AppTheme.spacingS),
            Text(
              title,
              style: AppTheme.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
                color:
                    isSelected ? AppTheme.primaryColor : AppTheme.textPrimary,
              ),
            ),
            const SizedBox(height: AppTheme.spacingXS),
            Text(
              description,
              style: AppTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
