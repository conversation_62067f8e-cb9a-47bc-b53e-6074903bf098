import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:intl/intl.dart';

class TherapistDashboard extends StatefulWidget {
  final String therapistId;
  final String therapistName;
  final String therapistEmail;

  const TherapistDashboard({
    super.key,
    required this.therapistId,
    required this.therapistName,
    required this.therapistEmail,
  });

  @override
  State<TherapistDashboard> createState() => _TherapistDashboardState();
}

class _TherapistDashboardState extends State<TherapistDashboard> {
  int _selectedIndex = 0;
  Map<String, dynamic> _therapistStats = {};
  List<dynamic> _todayAppointments = [];
  List<dynamic> _recentPatients = [];
  bool _isLoading = true;

  final List<String> _titles = [
    'Dashboard',
    'Patients',
    'Appointments',
    'Schedule',
    'Profile',
  ];

  @override
  void initState() {
    super.initState();
    _fetchTherapistData();
  }

  Future<void> _fetchTherapistData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Fetch therapist's appointments
      final appointmentsResponse = await http.get(
        Uri.parse('http://localhost:3000/api/appointments/therapist/${widget.therapistId}'),
      );

      // Fetch all users (patients)
      final usersResponse = await http.get(
        Uri.parse('http://localhost:3000/api/users'),
      );

      if (appointmentsResponse.statusCode == 200) {
        final appointments = json.decode(appointmentsResponse.body) as List;
        final today = DateTime.now();
        _todayAppointments = appointments.where((apt) {
          final aptDate = DateTime.parse(apt['date'] ?? '');
          return aptDate.year == today.year &&
                 aptDate.month == today.month &&
                 aptDate.day == today.day &&
                 apt['status'] == 'scheduled';
        }).toList();
      }

      if (usersResponse.statusCode == 200) {
        final users = json.decode(usersResponse.body) as List;
        _recentPatients = users.where((user) => user['role'] == 'user').take(5).toList();
      }

      setState(() {
        _therapistStats = {
          'todayAppointments': _todayAppointments.length,
          'totalPatients': _recentPatients.length,
          'weeklyAppointments': _todayAppointments.length * 5, // Mock data
          'completedSessions': 42, // Mock data
        };
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_titles[_selectedIndex]),
        backgroundColor: const Color(0xFF3B82F6),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchTherapistData,
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () => Navigator.pushReplacementNamed(context, '/'),
          ),
        ],
      ),
      drawer: _buildDrawer(),
      body: _selectedIndex == 0 ? _buildDashboard() : _buildComingSoon(),
    );
  }

  Widget _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          UserAccountsDrawerHeader(
            decoration: const BoxDecoration(
              color: Color(0xFF3B82F6),
            ),
            accountName: Text(
              'Dr. ${widget.therapistName}',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            accountEmail: Text(widget.therapistEmail),
            currentAccountPicture: CircleAvatar(
              backgroundColor: Colors.white,
              child: Text(
                widget.therapistName.isNotEmpty ? widget.therapistName[0].toUpperCase() : 'T',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF3B82F6),
                ),
              ),
            ),
          ),
          _buildDrawerItem(Icons.dashboard, 'Dashboard', 0),
          _buildDrawerItem(Icons.people, 'Patients', 1),
          _buildDrawerItem(Icons.calendar_today, 'Appointments', 2),
          _buildDrawerItem(Icons.schedule, 'Schedule', 3),
          _buildDrawerItem(Icons.person, 'Profile', 4),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.logout, color: Colors.red),
            title: const Text('Logout', style: TextStyle(color: Colors.red)),
            onTap: () => Navigator.pushReplacementNamed(context, '/'),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(IconData icon, String title, int index) {
    return ListTile(
      leading: Icon(
        icon,
        color: _selectedIndex == index ? const Color(0xFF3B82F6) : Colors.grey,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: _selectedIndex == index ? const Color(0xFF3B82F6) : Colors.black,
          fontWeight: _selectedIndex == index ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      selected: _selectedIndex == index,
      onTap: () {
        setState(() {
          _selectedIndex = index;
        });
        Navigator.pop(context);
      },
    );
  }

  Widget _buildDashboard() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: const Color(0xFF3B82F6),
                    child: Text(
                      widget.therapistName.isNotEmpty ? widget.therapistName[0].toUpperCase() : 'T',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Welcome, Dr. ${widget.therapistName}!',
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Ready to help your patients today?',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Stats Cards
          GridView.count(
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              _buildStatCard(
                'Today\'s Appointments',
                '${_therapistStats['todayAppointments'] ?? 0}',
                Icons.today,
                const Color(0xFF3B82F6),
              ),
              _buildStatCard(
                'Total Patients',
                '${_therapistStats['totalPatients'] ?? 0}',
                Icons.people,
                const Color(0xFF10B981),
              ),
              _buildStatCard(
                'This Week',
                '${_therapistStats['weeklyAppointments'] ?? 0}',
                Icons.calendar_week,
                const Color(0xFF8B5CF6),
              ),
              _buildStatCard(
                'Completed',
                '${_therapistStats['completedSessions'] ?? 0}',
                Icons.check_circle,
                const Color(0xFFF59E0B),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Today's Appointments
          const Text(
            'Today\'s Appointments',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _todayAppointments.isEmpty
              ? Card(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Center(
                      child: Column(
                        children: [
                          Icon(
                            Icons.event_available,
                            size: 48,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'No appointments today',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              : ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _todayAppointments.length,
                  itemBuilder: (context, index) {
                    final appointment = _todayAppointments[index];
                    return Card(
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: const Color(0xFF3B82F6),
                          child: Text(
                            appointment['patientName']?.substring(0, 1).toUpperCase() ?? 'P',
                            style: const TextStyle(color: Colors.white),
                          ),
                        ),
                        title: Text(appointment['patientName'] ?? 'Unknown Patient'),
                        subtitle: Text(appointment['type'] ?? 'Consultation'),
                        trailing: Text(
                          DateFormat('HH:mm').format(
                            DateTime.parse(appointment['date'] ?? ''),
                          ),
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF3B82F6),
                          ),
                        ),
                      ),
                    );
                  },
                ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComingSoon() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.construction,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Coming Soon!',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'This feature is under development',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }
}
