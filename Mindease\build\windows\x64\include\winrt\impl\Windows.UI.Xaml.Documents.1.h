// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_UI_Xaml_Documents_1_H
#define WINRT_Windows_UI_Xaml_Documents_1_H
#include "winrt/impl/Windows.UI.Xaml.Documents.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Documents
{
    struct __declspec(empty_bases) IBlock :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBlock>
    {
        IBlock(std::nullptr_t = nullptr) noexcept {}
        IBlock(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBlock2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBlock2>
    {
        IBlock2(std::nullptr_t = nullptr) noexcept {}
        IBlock2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBlockFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBlockFactory>
    {
        IBlockFactory(std::nullptr_t = nullptr) noexcept {}
        IBlockFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBlockStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBlockStatics>
    {
        IBlockStatics(std::nullptr_t = nullptr) noexcept {}
        IBlockStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBlockStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBlockStatics2>
    {
        IBlockStatics2(std::nullptr_t = nullptr) noexcept {}
        IBlockStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBold :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBold>
    {
        IBold(std::nullptr_t = nullptr) noexcept {}
        IBold(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContactContentLinkProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContactContentLinkProvider>
    {
        IContactContentLinkProvider(std::nullptr_t = nullptr) noexcept {}
        IContactContentLinkProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentLink :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentLink>
    {
        IContentLink(std::nullptr_t = nullptr) noexcept {}
        IContentLink(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentLinkInvokedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentLinkInvokedEventArgs>
    {
        IContentLinkInvokedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IContentLinkInvokedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentLinkProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentLinkProvider>
    {
        IContentLinkProvider(std::nullptr_t = nullptr) noexcept {}
        IContentLinkProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentLinkProviderCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentLinkProviderCollection>
    {
        IContentLinkProviderCollection(std::nullptr_t = nullptr) noexcept {}
        IContentLinkProviderCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentLinkProviderFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentLinkProviderFactory>
    {
        IContentLinkProviderFactory(std::nullptr_t = nullptr) noexcept {}
        IContentLinkProviderFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IContentLinkStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentLinkStatics>
    {
        IContentLinkStatics(std::nullptr_t = nullptr) noexcept {}
        IContentLinkStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGlyphs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGlyphs>
    {
        IGlyphs(std::nullptr_t = nullptr) noexcept {}
        IGlyphs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGlyphs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGlyphs2>
    {
        IGlyphs2(std::nullptr_t = nullptr) noexcept {}
        IGlyphs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGlyphsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGlyphsStatics>
    {
        IGlyphsStatics(std::nullptr_t = nullptr) noexcept {}
        IGlyphsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGlyphsStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGlyphsStatics2>
    {
        IGlyphsStatics2(std::nullptr_t = nullptr) noexcept {}
        IGlyphsStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHyperlink :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHyperlink>
    {
        IHyperlink(std::nullptr_t = nullptr) noexcept {}
        IHyperlink(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHyperlink2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHyperlink2>
    {
        IHyperlink2(std::nullptr_t = nullptr) noexcept {}
        IHyperlink2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHyperlink3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHyperlink3>
    {
        IHyperlink3(std::nullptr_t = nullptr) noexcept {}
        IHyperlink3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHyperlink4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHyperlink4>
    {
        IHyperlink4(std::nullptr_t = nullptr) noexcept {}
        IHyperlink4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHyperlink5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHyperlink5>
    {
        IHyperlink5(std::nullptr_t = nullptr) noexcept {}
        IHyperlink5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHyperlinkClickEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHyperlinkClickEventArgs>
    {
        IHyperlinkClickEventArgs(std::nullptr_t = nullptr) noexcept {}
        IHyperlinkClickEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHyperlinkStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHyperlinkStatics>
    {
        IHyperlinkStatics(std::nullptr_t = nullptr) noexcept {}
        IHyperlinkStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHyperlinkStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHyperlinkStatics2>
    {
        IHyperlinkStatics2(std::nullptr_t = nullptr) noexcept {}
        IHyperlinkStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHyperlinkStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHyperlinkStatics3>
    {
        IHyperlinkStatics3(std::nullptr_t = nullptr) noexcept {}
        IHyperlinkStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHyperlinkStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHyperlinkStatics4>
    {
        IHyperlinkStatics4(std::nullptr_t = nullptr) noexcept {}
        IHyperlinkStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHyperlinkStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHyperlinkStatics5>
    {
        IHyperlinkStatics5(std::nullptr_t = nullptr) noexcept {}
        IHyperlinkStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInline :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInline>
    {
        IInline(std::nullptr_t = nullptr) noexcept {}
        IInline(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInlineFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInlineFactory>
    {
        IInlineFactory(std::nullptr_t = nullptr) noexcept {}
        IInlineFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInlineUIContainer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInlineUIContainer>
    {
        IInlineUIContainer(std::nullptr_t = nullptr) noexcept {}
        IInlineUIContainer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItalic :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItalic>
    {
        IItalic(std::nullptr_t = nullptr) noexcept {}
        IItalic(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILineBreak :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILineBreak>
    {
        ILineBreak(std::nullptr_t = nullptr) noexcept {}
        ILineBreak(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IParagraph :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IParagraph>
    {
        IParagraph(std::nullptr_t = nullptr) noexcept {}
        IParagraph(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IParagraphStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IParagraphStatics>
    {
        IParagraphStatics(std::nullptr_t = nullptr) noexcept {}
        IParagraphStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPlaceContentLinkProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPlaceContentLinkProvider>
    {
        IPlaceContentLinkProvider(std::nullptr_t = nullptr) noexcept {}
        IPlaceContentLinkProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRun :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRun>
    {
        IRun(std::nullptr_t = nullptr) noexcept {}
        IRun(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRunStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRunStatics>
    {
        IRunStatics(std::nullptr_t = nullptr) noexcept {}
        IRunStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpan :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpan>
    {
        ISpan(std::nullptr_t = nullptr) noexcept {}
        ISpan(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpanFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpanFactory>
    {
        ISpanFactory(std::nullptr_t = nullptr) noexcept {}
        ISpanFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextElement :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextElement>
    {
        ITextElement(std::nullptr_t = nullptr) noexcept {}
        ITextElement(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextElement2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextElement2>
    {
        ITextElement2(std::nullptr_t = nullptr) noexcept {}
        ITextElement2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextElement3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextElement3>
    {
        ITextElement3(std::nullptr_t = nullptr) noexcept {}
        ITextElement3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextElement4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextElement4>
    {
        ITextElement4(std::nullptr_t = nullptr) noexcept {}
        ITextElement4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextElement5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextElement5>
    {
        ITextElement5(std::nullptr_t = nullptr) noexcept {}
        ITextElement5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextElementFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextElementFactory>
    {
        ITextElementFactory(std::nullptr_t = nullptr) noexcept {}
        ITextElementFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextElementOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextElementOverrides>
    {
        ITextElementOverrides(std::nullptr_t = nullptr) noexcept {}
        ITextElementOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextElementStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextElementStatics>
    {
        ITextElementStatics(std::nullptr_t = nullptr) noexcept {}
        ITextElementStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextElementStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextElementStatics2>
    {
        ITextElementStatics2(std::nullptr_t = nullptr) noexcept {}
        ITextElementStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextElementStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextElementStatics3>
    {
        ITextElementStatics3(std::nullptr_t = nullptr) noexcept {}
        ITextElementStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextElementStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextElementStatics4>
    {
        ITextElementStatics4(std::nullptr_t = nullptr) noexcept {}
        ITextElementStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextHighlighter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextHighlighter>
    {
        ITextHighlighter(std::nullptr_t = nullptr) noexcept {}
        ITextHighlighter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextHighlighterBase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextHighlighterBase>
    {
        ITextHighlighterBase(std::nullptr_t = nullptr) noexcept {}
        ITextHighlighterBase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextHighlighterBaseFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextHighlighterBaseFactory>
    {
        ITextHighlighterBaseFactory(std::nullptr_t = nullptr) noexcept {}
        ITextHighlighterBaseFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextHighlighterFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextHighlighterFactory>
    {
        ITextHighlighterFactory(std::nullptr_t = nullptr) noexcept {}
        ITextHighlighterFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextHighlighterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextHighlighterStatics>
    {
        ITextHighlighterStatics(std::nullptr_t = nullptr) noexcept {}
        ITextHighlighterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextPointer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextPointer>
    {
        ITextPointer(std::nullptr_t = nullptr) noexcept {}
        ITextPointer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITypography :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITypography>
    {
        ITypography(std::nullptr_t = nullptr) noexcept {}
        ITypography(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITypographyStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITypographyStatics>
    {
        ITypographyStatics(std::nullptr_t = nullptr) noexcept {}
        ITypographyStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUnderline :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUnderline>
    {
        IUnderline(std::nullptr_t = nullptr) noexcept {}
        IUnderline(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
