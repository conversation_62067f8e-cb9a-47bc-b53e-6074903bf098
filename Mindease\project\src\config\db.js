// MongoDB Database Configuration for MindEase App
const mongoose = require('mongoose');

const connectDB = async () => {
  console.log('🍃 Connecting to MongoDB Database...');

  try {
    // Connect to your existing MongoDB database
    const mongoURI = 'mongodb://localhost:27017/mindease';

    console.log('📡 Connecting to your existing MongoDB instance...');
    console.log('🌐 Database URI:', mongoURI);

    await mongoose.connect(mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 45000,
    });

    console.log('✅ MongoDB connected successfully!');
    console.log('🌐 Using existing database: mindease');

    // Test the connection
    const db = mongoose.connection.db;
    await db.admin().ping();
    console.log('🏓 MongoDB ping successful - Database is ready!');

    // Create indexes for better performance
    await createIndexes();

    // Migrate data from Firebase to MongoDB
    await migrateFirebaseToMongoDB();

    console.log('🚀 Database setup completed - Ready for real-time operations!');

  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    console.log('💡 Troubleshooting steps:');
    console.log('   1. Make sure MongoDB is running');
    console.log('   2. Check if port 27017 is available');
    console.log('   3. Verify database "mindease" exists');

    process.exit(1);
  }
};

// Create database indexes for better performance
const createIndexes = async () => {
  try {
    const db = mongoose.connection.db;

    // Users collection indexes
    await db.collection('users').createIndex({ email: 1 }, { unique: true });
    await db.collection('users').createIndex({ username: 1 }, { unique: true });
    await db.collection('users').createIndex({ role: 1 });
    await db.collection('users').createIndex({ isActive: 1 });

    // Therapists collection indexes
    await db.collection('therapists').createIndex({ email: 1 }, { unique: true });
    await db.collection('therapists').createIndex({ specialty: 1 });
    await db.collection('therapists').createIndex({ location: 1 });
    await db.collection('therapists').createIndex({ isApproved: 1 });
    await db.collection('therapists').createIndex({ rating: -1 });

    // Appointments collection indexes
    await db.collection('appointments').createIndex({ userId: 1 });
    await db.collection('appointments').createIndex({ therapistId: 1 });
    await db.collection('appointments').createIndex({ appointmentDate: 1 });
    await db.collection('appointments').createIndex({ status: 1 });

    // Mood entries collection indexes
    await db.collection('moodentries').createIndex({ userId: 1 });
    await db.collection('moodentries').createIndex({ date: -1 });
    await db.collection('moodentries').createIndex({ mood: 1 });

    // Journal entries collection indexes
    await db.collection('journalentries').createIndex({ userId: 1 });
    await db.collection('journalentries').createIndex({ date: -1 });

    // Test results collection indexes
    await db.collection('testresults').createIndex({ userId: 1 });
    await db.collection('testresults').createIndex({ testType: 1 });
    await db.collection('testresults').createIndex({ date: -1 });

    console.log('📊 Database indexes created successfully');
  } catch (error) {
    console.error('⚠️ Error creating indexes:', error.message);
  }
};

// Migrate data from Firebase to MongoDB
const migrateFirebaseToMongoDB = async () => {
  console.log('🔄 Starting Firebase to MongoDB migration...');

  try {
    const { initializeFirebase, getDb } = require('./firebase');
    const bcrypt = require('bcryptjs');

    // Initialize Firebase to get existing data
    await initializeFirebase();
    const firebaseDb = getDb();

    const db = mongoose.connection.db;

    // Migrate Users
    console.log('👥 Migrating users...');
    try {
      const usersQuery = await firebaseDb.collection('users').get();
      if (usersQuery.docs && usersQuery.docs.length > 0) {
        const users = usersQuery.docs.map(doc => ({ _id: doc.id, ...doc.data() }));

        for (const user of users) {
          await db.collection('users').updateOne(
            { email: user.email },
            { $set: user },
            { upsert: true }
          );
        }
        console.log(`✅ Migrated ${users.length} users`);
      } else {
        // Create default users if no Firebase data
        await createDefaultUsers(db);
      }
    } catch (error) {
      console.log('⚠️ Firebase users not available, creating default users...');
      await createDefaultUsers(db);
    }

    // Migrate Therapists
    console.log('👨‍⚕️ Migrating therapists...');
    try {
      const therapistsQuery = await firebaseDb.collection('therapists').get();
      if (therapistsQuery.docs && therapistsQuery.docs.length > 0) {
        const therapists = therapistsQuery.docs.map(doc => ({ _id: doc.id, ...doc.data() }));

        for (const therapist of therapists) {
          await db.collection('therapists').updateOne(
            { email: therapist.email },
            { $set: therapist },
            { upsert: true }
          );
        }
        console.log(`✅ Migrated ${therapists.length} therapists`);
      } else {
        await createDefaultTherapists(db);
      }
    } catch (error) {
      console.log('⚠️ Firebase therapists not available, creating default therapists...');
      await createDefaultTherapists(db);
    }

    // Migrate Mood Entries
    console.log('😊 Migrating mood entries...');
    try {
      const moodQuery = await firebaseDb.collection('moodEntries').get();
      if (moodQuery.docs && moodQuery.docs.length > 0) {
        const moodEntries = moodQuery.docs.map(doc => ({ _id: doc.id, ...doc.data() }));

        for (const mood of moodEntries) {
          await db.collection('moodentries').updateOne(
            { _id: mood._id },
            { $set: mood },
            { upsert: true }
          );
        }
        console.log(`✅ Migrated ${moodEntries.length} mood entries`);
      } else {
        await createDefaultMoodEntries(db);
      }
    } catch (error) {
      console.log('⚠️ Firebase mood entries not available, creating default entries...');
      await createDefaultMoodEntries(db);
    }

    // Migrate Reviews
    console.log('⭐ Migrating reviews...');
    try {
      const reviewsQuery = await firebaseDb.collection('reviews').get();
      if (reviewsQuery.docs && reviewsQuery.docs.length > 0) {
        const reviews = reviewsQuery.docs.map(doc => ({ _id: doc.id, ...doc.data() }));

        for (const review of reviews) {
          await db.collection('reviews').updateOne(
            { _id: review._id },
            { $set: review },
            { upsert: true }
          );
        }
        console.log(`✅ Migrated ${reviews.length} reviews`);
      } else {
        await createDefaultReviews(db);
      }
    } catch (error) {
      console.log('⚠️ Firebase reviews not available, creating default reviews...');
      await createDefaultReviews(db);
    }

    console.log('🎉 Migration completed successfully!');

  } catch (error) {
    console.log('⚠️ Firebase not available, checking existing MongoDB data...');

    try {
      // Check if data already exists
      const existingUsers = await mongoose.connection.db.collection('users').countDocuments();
      const existingTherapists = await mongoose.connection.db.collection('therapists').countDocuments();

      if (existingUsers > 0 || existingTherapists > 0) {
        console.log(`✅ Found existing data: ${existingUsers} users, ${existingTherapists} therapists`);
        console.log('🔄 Using existing MongoDB data - no migration needed');
      } else {
        console.log('📝 Creating initial data in MongoDB...');
        await createDefaultUsers(mongoose.connection.db);
        await createDefaultTherapists(mongoose.connection.db);
        await createDefaultMoodEntries(mongoose.connection.db);
        await createDefaultReviews(mongoose.connection.db);
      }
    } catch (dbError) {
      console.log('⚠️ Error checking existing data:', dbError.message);
      console.log('🔄 Continuing with existing database...');
    }
  }
};

// Create default users
const createDefaultUsers = async (db) => {
  const bcrypt = require('bcryptjs');

  const defaultUsers = [
    {
      _id: 'admin_user',
      username: 'admin',
      email: '<EMAIL>',
      passwordHash: await bcrypt.hash('admin123', 10),
      role: 'admin',
      isBlocked: false,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      _id: 'test_user',
      username: 'hadyy',
      email: '<EMAIL>',
      passwordHash: await bcrypt.hash('password123', 10),
      role: 'user',
      isBlocked: false,
      isActive: true,
      profile: {
        firstName: 'Hady',
        lastName: 'User',
        age: 25,
        gender: 'male',
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    }
  ];

  for (const user of defaultUsers) {
    await db.collection('users').updateOne(
      { email: user.email },
      { $set: user },
      { upsert: true }
    );
  }
  console.log('✅ Created default users');
};

// Create default therapists
const createDefaultTherapists = async (db) => {
  const bcrypt = require('bcryptjs');

  const defaultTherapists = [
    {
      _id: 'test_therapist',
      name: 'Dr. Sarah Johnson',
      email: '<EMAIL>',
      passwordHash: await bcrypt.hash('therapist123', 10),
      phone: '******-0123',
      specialty: 'Clinical Psychology',
      specialization: 'Anxiety & Depression',
      experience: 8,
      location: 'New York, NY',
      rating: 4.8,
      reviewCount: 127,
      hourlyRate: 120,
      bio: 'Dr. Sarah Johnson is a licensed clinical psychologist with over 8 years of experience helping individuals overcome anxiety, depression, and trauma.',
      education: 'PhD in Clinical Psychology, Harvard University',
      languages: ['English', 'Spanish'],
      approaches: ['Cognitive Behavioral Therapy', 'Mindfulness-Based Therapy', 'Trauma-Informed Care'],
      availability: 'Mon-Fri: 9AM-6PM',
      isApproved: true,
      isBlocked: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    }
  ];

  for (const therapist of defaultTherapists) {
    await db.collection('therapists').updateOne(
      { email: therapist.email },
      { $set: therapist },
      { upsert: true }
    );
  }
  console.log('✅ Created default therapists');
};

// Create default mood entries
const createDefaultMoodEntries = async (db) => {
  const defaultMoods = [
    {
      _id: 'mood_1',
      userId: 'test_user',
      mood: 'happy',
      intensity: 8,
      note: 'Had a great day at work!',
      date: new Date(Date.now() - 86400000),
      createdAt: new Date(Date.now() - 86400000),
    },
    {
      _id: 'mood_2',
      userId: 'test_user',
      mood: 'anxious',
      intensity: 6,
      note: 'Feeling worried about upcoming presentation',
      date: new Date(Date.now() - 172800000),
      createdAt: new Date(Date.now() - 172800000),
    },
    {
      _id: 'mood_3',
      userId: 'test_user',
      mood: 'calm',
      intensity: 7,
      note: 'Meditation session helped a lot',
      date: new Date(Date.now() - 259200000),
      createdAt: new Date(Date.now() - 259200000),
    }
  ];

  for (const mood of defaultMoods) {
    await db.collection('moodentries').updateOne(
      { _id: mood._id },
      { $set: mood },
      { upsert: true }
    );
  }
  console.log('✅ Created default mood entries');
};

// Create default reviews
const createDefaultReviews = async (db) => {
  const defaultReviews = [
    {
      _id: 'review_1',
      therapistId: 'test_therapist',
      userId: 'test_user',
      userName: 'John D.',
      rating: 5,
      comment: 'Dr. Johnson has been incredibly helpful. Her approach is both professional and compassionate.',
      date: new Date('2024-01-15'),
      createdAt: new Date('2024-01-15'),
    },
    {
      _id: 'review_2',
      therapistId: 'test_therapist',
      userId: 'user_2',
      userName: 'Maria S.',
      rating: 5,
      comment: 'Excellent therapist! I\'ve seen significant improvement in my anxiety levels.',
      date: new Date('2024-01-10'),
      createdAt: new Date('2024-01-10'),
    }
  ];

  for (const review of defaultReviews) {
    await db.collection('reviews').updateOne(
      { _id: review._id },
      { $set: review },
      { upsert: true }
    );
  }
  console.log('✅ Created default reviews');
};

// Handle connection events
mongoose.connection.on('connected', () => {
  console.log('🔗 Mongoose connected to MongoDB');
});

mongoose.connection.on('error', (err) => {
  console.error('🔥 Mongoose connection error:', err);
});

mongoose.connection.on('disconnected', () => {
  console.log('📡 Mongoose disconnected from MongoDB');
});

// Graceful shutdown
process.on('SIGINT', async () => {
  await mongoose.connection.close();
  console.log('🛑 MongoDB connection closed through app termination');
  process.exit(0);
});

module.exports = connectDB;
