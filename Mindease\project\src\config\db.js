// MongoDB Database Configuration for MindEase App
const mongoose = require('mongoose');

const connectDB = async () => {
  console.log('🍃 Connecting to MongoDB Database...');

  try {
    // Connect to your existing MongoDB database
    const mongoURI = 'mongodb://localhost:27017/mindease';

    console.log('📡 Connecting to your existing MongoDB instance...');
    console.log('🌐 Database URI:', mongoURI);

    await mongoose.connect(mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 45000,
    });

    console.log('✅ MongoDB connected successfully!');
    console.log('🌐 Using existing database: mindease');

    // Test the connection
    const db = mongoose.connection.db;
    await db.admin().ping();
    console.log('🏓 MongoDB ping successful - Database is ready!');

    // Create indexes for better performance
    await createIndexes();

    // Check and create initial data if needed
    await checkAndCreateInitialData();

    console.log('🚀 Database setup completed - Ready for real-time operations!');

  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    console.log('💡 Troubleshooting steps:');
    console.log('   1. Make sure MongoDB is running');
    console.log('   2. Check if port 27017 is available');
    console.log('   3. Verify database "mindease" exists');

    process.exit(1);
  }
};

// Create database indexes for better performance
const createIndexes = async () => {
  try {
    const db = mongoose.connection.db;

    // Users collection indexes
    await db.collection('users').createIndex({ email: 1 }, { unique: true });
    await db.collection('users').createIndex({ username: 1 }, { unique: true });
    await db.collection('users').createIndex({ role: 1 });
    await db.collection('users').createIndex({ isActive: 1 });

    // Therapists collection indexes
    await db.collection('therapists').createIndex({ email: 1 }, { unique: true });
    await db.collection('therapists').createIndex({ specialty: 1 });
    await db.collection('therapists').createIndex({ location: 1 });
    await db.collection('therapists').createIndex({ isApproved: 1 });
    await db.collection('therapists').createIndex({ rating: -1 });

    // Appointments collection indexes
    await db.collection('appointments').createIndex({ userId: 1 });
    await db.collection('appointments').createIndex({ therapistId: 1 });
    await db.collection('appointments').createIndex({ appointmentDate: 1 });
    await db.collection('appointments').createIndex({ status: 1 });

    // Mood entries collection indexes
    await db.collection('moodentries').createIndex({ userId: 1 });
    await db.collection('moodentries').createIndex({ date: -1 });
    await db.collection('moodentries').createIndex({ mood: 1 });

    // Journal entries collection indexes
    await db.collection('journalentries').createIndex({ userId: 1 });
    await db.collection('journalentries').createIndex({ date: -1 });

    // Test results collection indexes
    await db.collection('testresults').createIndex({ userId: 1 });
    await db.collection('testresults').createIndex({ testType: 1 });
    await db.collection('testresults').createIndex({ date: -1 });

    console.log('📊 Database indexes created successfully');
  } catch (error) {
    console.error('⚠️ Error creating indexes:', error.message);
  }
};

// Check and create initial data if needed
const checkAndCreateInitialData = async () => {
  console.log('🔍 Checking existing MongoDB data...');

  try {
    const db = mongoose.connection.db;

    // Check if data already exists
    const existingUsers = await db.collection('users').countDocuments();
    const existingTherapists = await db.collection('therapists').countDocuments();
    const existingMoods = await db.collection('moods').countDocuments();
    const existingReviews = await db.collection('reviews').countDocuments();

    console.log(`📊 Current data: ${existingUsers} users, ${existingTherapists} therapists, ${existingMoods} moods, ${existingReviews} reviews`);

    if (existingUsers === 0) {
      console.log('👥 Creating default users...');
      await createDefaultUsers(db);
    }

    if (existingTherapists === 0) {
      console.log('👨‍⚕️ Creating default therapists...');
      await createDefaultTherapists(db);
    }

    if (existingMoods === 0) {
      console.log('😊 Creating default mood entries...');
      await createDefaultMoodEntries(db);
    }

    if (existingReviews === 0) {
      console.log('⭐ Creating default reviews...');
      await createDefaultReviews(db);
    }

    console.log('✅ Data initialization completed!');

  } catch (error) {
    console.log('⚠️ Error checking/creating data:', error.message);
    console.log('🔄 Continuing with existing database...');
  }
};

// Create default users
const createDefaultUsers = async (db) => {
  const bcrypt = require('bcryptjs');

  const defaultUsers = [
    {
      _id: 'admin_user',
      username: 'admin',
      email: '<EMAIL>',
      passwordHash: await bcrypt.hash('admin123', 10),
      role: 'admin',
      isBlocked: false,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      _id: 'test_user',
      username: 'hadyy',
      email: '<EMAIL>',
      passwordHash: await bcrypt.hash('password123', 10),
      role: 'user',
      isBlocked: false,
      isActive: true,
      profile: {
        firstName: 'Hady',
        lastName: 'User',
        age: 25,
        gender: 'male',
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    }
  ];

  for (const user of defaultUsers) {
    await db.collection('users').updateOne(
      { email: user.email },
      { $set: user },
      { upsert: true }
    );
  }
  console.log('✅ Created default users');
};

// Create default therapists
const createDefaultTherapists = async (db) => {
  const bcrypt = require('bcryptjs');

  const defaultTherapists = [
    {
      _id: 'test_therapist',
      name: 'Dr. Sarah Johnson',
      email: '<EMAIL>',
      passwordHash: await bcrypt.hash('therapist123', 10),
      phone: '******-0123',
      specialty: 'Clinical Psychology',
      specialization: 'Anxiety & Depression',
      experience: 8,
      location: 'New York, NY',
      rating: 4.8,
      reviewCount: 127,
      hourlyRate: 120,
      bio: 'Dr. Sarah Johnson is a licensed clinical psychologist with over 8 years of experience helping individuals overcome anxiety, depression, and trauma.',
      education: 'PhD in Clinical Psychology, Harvard University',
      languages: ['English', 'Spanish'],
      approaches: ['Cognitive Behavioral Therapy', 'Mindfulness-Based Therapy', 'Trauma-Informed Care'],
      availability: 'Mon-Fri: 9AM-6PM',
      isApproved: true,
      isBlocked: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    }
  ];

  for (const therapist of defaultTherapists) {
    await db.collection('therapists').updateOne(
      { email: therapist.email },
      { $set: therapist },
      { upsert: true }
    );
  }
  console.log('✅ Created default therapists');
};

// Create default mood entries
const createDefaultMoodEntries = async (db) => {
  const defaultMoods = [
    {
      _id: 'mood_1',
      userId: 'test_user',
      mood: 'happy',
      intensity: 8,
      note: 'Had a great day at work!',
      date: new Date(Date.now() - 86400000),
      createdAt: new Date(Date.now() - 86400000),
    },
    {
      _id: 'mood_2',
      userId: 'test_user',
      mood: 'anxious',
      intensity: 6,
      note: 'Feeling worried about upcoming presentation',
      date: new Date(Date.now() - 172800000),
      createdAt: new Date(Date.now() - 172800000),
    },
    {
      _id: 'mood_3',
      userId: 'test_user',
      mood: 'calm',
      intensity: 7,
      note: 'Meditation session helped a lot',
      date: new Date(Date.now() - 259200000),
      createdAt: new Date(Date.now() - 259200000),
    }
  ];

  for (const mood of defaultMoods) {
    await db.collection('moodentries').updateOne(
      { _id: mood._id },
      { $set: mood },
      { upsert: true }
    );
  }
  console.log('✅ Created default mood entries');
};

// Create default reviews
const createDefaultReviews = async (db) => {
  const defaultReviews = [
    {
      _id: 'review_1',
      therapistId: 'test_therapist',
      userId: 'test_user',
      userName: 'John D.',
      rating: 5,
      comment: 'Dr. Johnson has been incredibly helpful. Her approach is both professional and compassionate.',
      date: new Date('2024-01-15'),
      createdAt: new Date('2024-01-15'),
    },
    {
      _id: 'review_2',
      therapistId: 'test_therapist',
      userId: 'user_2',
      userName: 'Maria S.',
      rating: 5,
      comment: 'Excellent therapist! I\'ve seen significant improvement in my anxiety levels.',
      date: new Date('2024-01-10'),
      createdAt: new Date('2024-01-10'),
    }
  ];

  for (const review of defaultReviews) {
    await db.collection('reviews').updateOne(
      { _id: review._id },
      { $set: review },
      { upsert: true }
    );
  }
  console.log('✅ Created default reviews');
};

// Handle connection events
mongoose.connection.on('connected', () => {
  console.log('🔗 Mongoose connected to MongoDB');
});

mongoose.connection.on('error', (err) => {
  console.error('🔥 Mongoose connection error:', err);
});

mongoose.connection.on('disconnected', () => {
  console.log('📡 Mongoose disconnected from MongoDB');
});

// Graceful shutdown
process.on('SIGINT', async () => {
  await mongoose.connection.close();
  console.log('🛑 MongoDB connection closed through app termination');
  process.exit(0);
});

module.exports = connectDB;
