// Firebase Database Configuration for MindEase App
const { initializeFirebase, getDb } = require('./firebase');

const connectDB = async () => {
  console.log('🔥 Connecting to Firebase Database...');

  try {
    // Initialize Firebase
    await initializeFirebase();

    console.log('✅ Firebase connected successfully!');
    console.log('🌐 Using Firebase Firestore database');
    console.log('🚀 Database setup completed - Ready for real-time operations!');

  } catch (error) {
    console.error('❌ Firebase connection failed:', error.message);
    console.log('💡 Using fallback mock database for development');

    // Firebase initialization handles fallback automatically
    console.log('🔧 Mock database is ready for development');
  }
};

// Export the connection function and database getter
module.exports = {
  connectDB,
  getDb
};
