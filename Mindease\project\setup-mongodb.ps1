# MongoDB Setup Script for MindEase App
Write-Host "🚀 Setting up MongoDB for MindEase App..." -ForegroundColor Green

# Function to test if MongoDB is running
function Test-MongoConnection {
    try {
        $result = Invoke-WebRequest -Uri "http://localhost:27017" -TimeoutSec 2 -ErrorAction Stop
        return $true
    }
    catch {
        return $false
    }
}

# Function to find MongoDB installation
function Find-MongoDB {
    $possiblePaths = @(
        "C:\Program Files\MongoDB\Server\7.0\bin\mongod.exe",
        "C:\Program Files\MongoDB\Server\6.0\bin\mongod.exe",
        "C:\Program Files\MongoDB\Server\5.0\bin\mongod.exe",
        "C:\Program Files\MongoDB\Server\4.4\bin\mongod.exe",
        "C:\mongodb\bin\mongod.exe"
    )
    
    foreach ($path in $possiblePaths) {
        if (Test-Path $path) {
            Write-Host "✅ Found MongoDB at: $path" -ForegroundColor Green
            return $path
        }
    }
    
    # Try to find mongod.exe in PATH
    try {
        $mongoPath = Get-Command mongod.exe -ErrorAction Stop
        Write-Host "✅ Found MongoDB in PATH: $($mongoPath.Source)" -ForegroundColor Green
        return $mongoPath.Source
    }
    catch {
        return $null
    }
}

# Check if MongoDB is already running
Write-Host "🔍 Checking if MongoDB is already running..." -ForegroundColor Yellow
if (Test-MongoConnection) {
    Write-Host "✅ MongoDB is already running on localhost:27017!" -ForegroundColor Green
    Write-Host "🎉 You can now connect with MongoDB Compass using: mongodb://localhost:27017" -ForegroundColor Cyan
    exit 0
}

# Try to start MongoDB service first
Write-Host "🔧 Attempting to start MongoDB service..." -ForegroundColor Yellow
try {
    Start-Service -Name "MongoDB" -ErrorAction Stop
    Start-Sleep -Seconds 3
    
    if (Test-MongoConnection) {
        Write-Host "✅ MongoDB service started successfully!" -ForegroundColor Green
        Write-Host "🎉 You can now connect with MongoDB Compass using: mongodb://localhost:27017" -ForegroundColor Cyan
        exit 0
    }
}
catch {
    Write-Host "⚠️ MongoDB service not found or failed to start" -ForegroundColor Yellow
}

# Find MongoDB installation
Write-Host "🔍 Looking for MongoDB installation..." -ForegroundColor Yellow
$mongoPath = Find-MongoDB

if (-not $mongoPath) {
    Write-Host "❌ MongoDB not found!" -ForegroundColor Red
    Write-Host "📥 Please install MongoDB Community Server from:" -ForegroundColor Yellow
    Write-Host "   https://www.mongodb.com/try/download/community" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "💡 Installation Tips:" -ForegroundColor Yellow
    Write-Host "   1. Download MongoDB Community Server" -ForegroundColor White
    Write-Host "   2. Install with default settings" -ForegroundColor White
    Write-Host "   3. Make sure to install as a Windows Service" -ForegroundColor White
    Write-Host "   4. Run this script again after installation" -ForegroundColor White
    exit 1
}

# Create data directory
$dataDir = "C:\data\db"
if (-not (Test-Path $dataDir)) {
    Write-Host "📁 Creating MongoDB data directory: $dataDir" -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $dataDir -Force | Out-Null
}

# Start MongoDB manually
Write-Host "🚀 Starting MongoDB server..." -ForegroundColor Yellow
Write-Host "📂 Data directory: $dataDir" -ForegroundColor Gray
Write-Host "🌐 Server will be available at: mongodb://localhost:27017" -ForegroundColor Gray

try {
    # Start MongoDB in a new window
    $mongoArgs = @("--dbpath", "`"$dataDir`"", "--port", "27017")
    Start-Process -FilePath $mongoPath -ArgumentList $mongoArgs -WindowStyle Normal

    # Wait for MongoDB to start
    Write-Host "⏳ Waiting for MongoDB to start..." -ForegroundColor Yellow
    $timeout = 30
    $elapsed = 0

    while ($elapsed -lt $timeout) {
        Start-Sleep -Seconds 1
        $elapsed++

        if (Test-MongoConnection) {
            Write-Host "✅ MongoDB started successfully!" -ForegroundColor Green
            Write-Host ""
            Write-Host "🎉 MongoDB is now running!" -ForegroundColor Green
            Write-Host "🔗 Connection String: mongodb://localhost:27017" -ForegroundColor Cyan
            Write-Host "🧭 MongoDB Compass Connection:" -ForegroundColor Yellow
            Write-Host "   1. Open MongoDB Compass" -ForegroundColor White
            Write-Host "   2. Use connection string: mongodb://localhost:27017" -ForegroundColor Cyan
            Write-Host "   3. Click 'Connect'" -ForegroundColor White
            Write-Host ""
            Write-Host "🚀 Your Node.js app can now connect to MongoDB!" -ForegroundColor Green
            exit 0
        }

        Write-Host "." -NoNewline -ForegroundColor Yellow
    }

    Write-Host ""
    Write-Host "❌ MongoDB failed to start within $timeout seconds" -ForegroundColor Red
    Write-Host "💡 Try running MongoDB manually:" -ForegroundColor Yellow
    Write-Host "   $mongoPath --dbpath `"$dataDir`"" -ForegroundColor Cyan

} catch {
    Write-Host "❌ Failed to start MongoDB: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 Try running as Administrator or check if port 27017 is available" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🔧 Troubleshooting:" -ForegroundColor Yellow
Write-Host "   1. Make sure port 27017 is not in use" -ForegroundColor White
Write-Host "   2. Run PowerShell as Administrator" -ForegroundColor White
Write-Host "   3. Check Windows Firewall settings" -ForegroundColor White
Write-Host "   4. Restart your computer and try again" -ForegroundColor White
