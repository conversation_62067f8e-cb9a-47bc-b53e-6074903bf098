import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:intl/intl.dart';

class UserDashboard extends StatefulWidget {
  final String userId;
  final String userName;
  final String userEmail;

  const UserDashboard({
    super.key,
    required this.userId,
    required this.userName,
    required this.userEmail,
  });

  @override
  State<UserDashboard> createState() => _UserDashboardState();
}

class _UserDashboardState extends State<UserDashboard> {
  int _selectedIndex = 0;
  Map<String, dynamic> _userStats = {};
  List<dynamic> _recentMoods = [];
  List<dynamic> _upcomingAppointments = [];
  bool _isLoading = true;

  final List<String> _titles = [
    'Dashboard',
    'Mood Tracker',
    'Appointments',
    'Journal',
    'Profile',
  ];

  @override
  void initState() {
    super.initState();
    _fetchUserData();
  }

  Future<void> _fetchUserData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Fetch user's mood entries
      final moodResponse = await http.get(
        Uri.parse('http://localhost:3000/api/mood/user/${widget.userId}'),
      );

      // Fetch user's appointments
      final appointmentsResponse = await http.get(
        Uri.parse('http://localhost:3000/api/appointments/user/${widget.userId}'),
      );

      if (moodResponse.statusCode == 200) {
        final moods = json.decode(moodResponse.body) as List;
        _recentMoods = moods.take(5).toList();
      }

      if (appointmentsResponse.statusCode == 200) {
        final appointments = json.decode(appointmentsResponse.body) as List;
        _upcomingAppointments = appointments
            .where((apt) => apt['status'] == 'scheduled')
            .take(3)
            .toList();
      }

      setState(() {
        _userStats = {
          'totalMoods': _recentMoods.length,
          'upcomingAppointments': _upcomingAppointments.length,
          'weeklyMoods': _recentMoods.where((mood) {
            final moodDate = DateTime.parse(mood['date'] ?? '');
            final weekAgo = DateTime.now().subtract(const Duration(days: 7));
            return moodDate.isAfter(weekAgo);
          }).length,
        };
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_titles[_selectedIndex]),
        backgroundColor: const Color(0xFF10B981),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchUserData,
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () => Navigator.pushReplacementNamed(context, '/'),
          ),
        ],
      ),
      drawer: _buildDrawer(),
      body: _selectedIndex == 0 ? _buildDashboard() : _buildComingSoon(),
    );
  }

  Widget _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          UserAccountsDrawerHeader(
            decoration: const BoxDecoration(
              color: Color(0xFF10B981),
            ),
            accountName: Text(
              widget.userName,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            accountEmail: Text(widget.userEmail),
            currentAccountPicture: CircleAvatar(
              backgroundColor: Colors.white,
              child: Text(
                widget.userName.isNotEmpty ? widget.userName[0].toUpperCase() : 'U',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF10B981),
                ),
              ),
            ),
          ),
          _buildDrawerItem(Icons.dashboard, 'Dashboard', 0),
          _buildDrawerItem(Icons.mood, 'Mood Tracker', 1),
          _buildDrawerItem(Icons.calendar_today, 'Appointments', 2),
          _buildDrawerItem(Icons.book, 'Journal', 3),
          _buildDrawerItem(Icons.person, 'Profile', 4),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.logout, color: Colors.red),
            title: const Text('Logout', style: TextStyle(color: Colors.red)),
            onTap: () => Navigator.pushReplacementNamed(context, '/'),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(IconData icon, String title, int index) {
    return ListTile(
      leading: Icon(
        icon,
        color: _selectedIndex == index ? const Color(0xFF10B981) : Colors.grey,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: _selectedIndex == index ? const Color(0xFF10B981) : Colors.black,
          fontWeight: _selectedIndex == index ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      selected: _selectedIndex == index,
      onTap: () {
        setState(() {
          _selectedIndex = index;
        });
        Navigator.pop(context);
      },
    );
  }

  Widget _buildDashboard() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: const Color(0xFF10B981),
                    child: Text(
                      widget.userName.isNotEmpty ? widget.userName[0].toUpperCase() : 'U',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Welcome back, ${widget.userName}!',
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'How are you feeling today?',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Stats Cards
          GridView.count(
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              _buildStatCard(
                'Total Moods',
                '${_userStats['totalMoods'] ?? 0}',
                Icons.mood,
                const Color(0xFF10B981),
              ),
              _buildStatCard(
                'This Week',
                '${_userStats['weeklyMoods'] ?? 0}',
                Icons.calendar_week,
                const Color(0xFF3B82F6),
              ),
              _buildStatCard(
                'Upcoming',
                '${_userStats['upcomingAppointments'] ?? 0}',
                Icons.event,
                const Color(0xFF8B5CF6),
              ),
              _buildStatCard(
                'Streak',
                '0 days',
                Icons.local_fire_department,
                const Color(0xFFF59E0B),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Recent Moods
          const Text(
            'Recent Mood Entries',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _recentMoods.isEmpty
              ? Card(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Center(
                      child: Column(
                        children: [
                          Icon(
                            Icons.mood_outlined,
                            size: 48,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'No mood entries yet',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              : ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _recentMoods.length,
                  itemBuilder: (context, index) {
                    final mood = _recentMoods[index];
                    return Card(
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: _getMoodColor(mood['mood']),
                          child: Text(
                            _getMoodEmoji(mood['mood']),
                            style: const TextStyle(fontSize: 20),
                          ),
                        ),
                        title: Text(mood['mood'] ?? 'Unknown'),
                        subtitle: Text(mood['note'] ?? 'No note'),
                        trailing: Text(
                          DateFormat('MMM dd').format(
                            DateTime.parse(mood['date'] ?? ''),
                          ),
                        ),
                      ),
                    );
                  },
                ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComingSoon() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.construction,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Coming Soon!',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'This feature is under development',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Color _getMoodColor(String mood) {
    switch (mood.toLowerCase()) {
      case 'happy':
        return Colors.green;
      case 'sad':
        return Colors.blue;
      case 'angry':
        return Colors.red;
      case 'anxious':
        return Colors.orange;
      case 'calm':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  String _getMoodEmoji(String mood) {
    switch (mood.toLowerCase()) {
      case 'happy':
        return '😊';
      case 'sad':
        return '😢';
      case 'angry':
        return '😠';
      case 'anxious':
        return '😰';
      case 'calm':
        return '😌';
      default:
        return '😐';
    }
  }
}
