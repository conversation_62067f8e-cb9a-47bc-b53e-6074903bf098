# MindEase Portal Structure

This document outlines the separate portal structure for the MindEase mental health application.

## 🏗️ Portal Architecture

The MindEase application now consists of **4 separate portals**, each designed for specific user roles:

### 1. 📱 **Main Mobile App** (`/Mindease/project/`)
- **Purpose**: Primary mobile application for end users
- **Technology**: React Native / Flutter
- **Users**: General users seeking mental health support
- **Features**: 
  - Mood tracking
  - Journal entries
  - Appointment booking
  - AI analysis
  - Progress tracking

### 2. 👤 **User Portal** (`/user_portal/`)
- **Purpose**: Web dashboard for registered users
- **Technology**: Flutter Web
- **Users**: Registered users who prefer web interface
- **Features**:
  - Dashboard with mood statistics
  - Recent mood entries
  - Upcoming appointments
  - Profile management
  - Progress visualization

### 3. 👨‍⚕️ **Therapist Portal** (`/therapist_portal/`)
- **Purpose**: Professional dashboard for therapists
- **Technology**: Flutter Web
- **Users**: Licensed therapists and mental health professionals
- **Features**:
  - Patient management
  - Appointment scheduling
  - Session notes
  - Patient progress tracking
  - Professional analytics

### 4. 🔧 **Admin Portal** (`/admin_portal/`)
- **Purpose**: Administrative dashboard for system management
- **Technology**: Flutter Web
- **Users**: System administrators and support staff
- **Features**:
  - User management
  - Therapist verification
  - System monitoring
  - Content moderation
  - Analytics and reporting

## 🎨 Design System

Each portal follows a consistent design system with role-specific color schemes:

### Color Schemes:
- **User Portal**: Emerald Green (`#10B981`) - Calming and growth-focused
- **Therapist Portal**: Blue (`#3B82F6`) - Professional and trustworthy
- **Admin Portal**: Purple (`#8B5CF6`) - Authority and control

### Common Design Elements:
- Material Design 3
- Rounded corners (12px border radius)
- Consistent typography
- Responsive layouts
- Accessibility compliance

## 🚀 Getting Started

### Prerequisites:
- Flutter SDK (latest stable)
- Node.js (for backend API)
- MongoDB (localhost:27017)

### Running the Portals:

#### 1. Start the Backend API:
```bash
cd Mindease/project
npm install
npm start
```

#### 2. User Portal:
```bash
cd user_portal
flutter pub get
flutter run -d chrome
```

#### 3. Therapist Portal:
```bash
cd therapist_portal
flutter pub get
flutter run -d chrome
```

#### 4. Admin Portal:
```bash
cd admin_portal
flutter pub get
flutter run -d chrome
```

## 🔐 Authentication & Access

### User Portal:
- **Login**: `POST /api/users/login`
- **Signup**: `POST /api/users/signup`
- **Access Level**: Standard user features

### Therapist Portal:
- **Login**: `POST /api/therapists/login`
- **Registration**: Contact admin for account creation
- **Access Level**: Professional features + patient management

### Admin Portal:
- **Login**: `POST /api/admin/login`
- **Access**: Super admin privileges
- **Features**: Full system control

## 📊 API Integration

All portals connect to the same backend API (`http://localhost:3000`) with different endpoints:

### Shared Endpoints:
- `/api/users` - User management
- `/api/therapists` - Therapist management
- `/api/appointments` - Appointment system
- `/api/mood` - Mood tracking
- `/api/journal` - Journal entries

### Portal-Specific Features:
- **User Portal**: Focus on personal data and self-management
- **Therapist Portal**: Patient overview and professional tools
- **Admin Portal**: System-wide management and analytics

## 🔄 Data Flow

```
[Mobile App] ←→ [Backend API] ←→ [MongoDB]
     ↑              ↑              ↑
[User Portal] [Therapist Portal] [Admin Portal]
```

All portals share the same database and API, ensuring data consistency across platforms.

## 🛠️ Development Guidelines

### Adding New Features:
1. **Backend**: Add API endpoints in `/Mindease/project/src/`
2. **User Portal**: Add user-focused features
3. **Therapist Portal**: Add professional tools
4. **Admin Portal**: Add management capabilities

### Code Structure:
```
portal_name/
├── lib/
│   ├── main.dart              # App entry point
│   ├── login_page.dart        # Authentication
│   ├── dashboard.dart         # Main dashboard
│   ├── models/               # Data models
│   ├── services/             # API services
│   └── widgets/              # Reusable components
├── pubspec.yaml              # Dependencies
└── README.md                 # Portal-specific docs
```

## 🔒 Security Considerations

- **Authentication**: JWT tokens for session management
- **Authorization**: Role-based access control
- **Data Privacy**: HIPAA compliance for patient data
- **API Security**: Rate limiting and input validation

## 📱 Responsive Design

All portals are designed to work across devices:
- **Desktop**: Primary target for professional use
- **Tablet**: Optimized layouts for touch interaction
- **Mobile**: Responsive design for on-the-go access

## 🚀 Deployment

### Development:
- Local development with hot reload
- Shared backend API on localhost:3000

### Production:
- Separate domains for each portal
- Shared backend API with load balancing
- CDN for static assets

## 📈 Future Enhancements

### Planned Features:
- **Video Calling**: Integrated telehealth sessions
- **Real-time Chat**: Secure messaging between users and therapists
- **Advanced Analytics**: ML-powered insights
- **Mobile Apps**: Native iOS/Android versions of portals

### Technical Improvements:
- **Offline Support**: PWA capabilities
- **Performance**: Code splitting and lazy loading
- **Accessibility**: Enhanced screen reader support
- **Internationalization**: Multi-language support

---

## 📞 Support

For technical support or questions about the portal structure:
- **Backend Issues**: Check API logs in `/Mindease/project/`
- **Portal Issues**: Check browser console for errors
- **Database Issues**: Verify MongoDB connection

Each portal is designed to provide a tailored experience for its specific user base while maintaining consistency and shared functionality through the common backend API.
