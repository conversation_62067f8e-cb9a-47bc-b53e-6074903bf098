# 🍃 MongoDB Setup Guide for MindEase App

## 📋 Current Status
- ✅ MongoDB Compass is installed
- ❌ MongoDB Server is not running
- ❌ Connection to localhost:27017 failed

## 🚀 Quick Solution Options

### Option 1: Install MongoDB Community Server (Recommended)

1. **Download MongoDB Community Server**
   - Go to: https://www.mongodb.com/try/download/community
   - Select: Windows x64
   - Download the MSI installer

2. **Install MongoDB**
   - Run the downloaded MSI file as Administrator
   - Choose "Complete" installation
   - ✅ **IMPORTANT**: Check "Install MongoDB as a Service"
   - ✅ **IMPORTANT**: Check "Run service as Network Service user"
   - Install MongoDB Compass (if not already installed)

3. **Verify Installation**
   - Open Command Prompt as Administrator
   - Run: `net start MongoDB`
   - Should see: "The MongoDB service was started successfully"

### Option 2: Use Docker (Alternative)

If you have Docker Desktop installed:

```bash
# Pull and run MongoDB container
docker run -d --name mongodb -p 27017:27017 mongo:latest

# Verify it's running
docker ps
```

### Option 3: Portable MongoDB (Quick Setup)

1. Download MongoDB ZIP from the same download page
2. Extract to `C:\mongodb`
3. Create data directory: `C:\data\db`
4. Run: `C:\mongodb\bin\mongod.exe --dbpath C:\data\db`

## 🧭 MongoDB Compass Connection

Once MongoDB is running, connect with Compass:

1. **Open MongoDB Compass**
2. **Connection String**: `mongodb://localhost:27017`
3. **Click "Connect"**

## 🔧 Troubleshooting

### If MongoDB service won't start:
```cmd
# Run as Administrator
net stop MongoDB
net start MongoDB
```

### If port 27017 is in use:
```cmd
# Check what's using port 27017
netstat -ano | findstr :27017

# Kill the process (replace PID with actual process ID)
taskkill /PID <PID> /F
```

### Manual MongoDB startup:
```cmd
# Create data directory
mkdir C:\data\db

# Start MongoDB manually
"C:\Program Files\MongoDB\Server\7.0\bin\mongod.exe" --dbpath "C:\data\db"
```

## ✅ Verification Steps

1. **Check if MongoDB is running**:
   ```cmd
   netstat -an | findstr 27017
   ```
   Should show: `TCP 0.0.0.0:27017 0.0.0.0:0 LISTENING`

2. **Test connection**:
   ```cmd
   mongo --eval "db.runCommand({ping:1})"
   ```
   Should return: `{ "ok" : 1 }`

3. **MongoDB Compass**: Should connect successfully to `mongodb://localhost:27017`

## 🚀 After MongoDB is Running

1. **Restart your Node.js backend**:
   ```cmd
   cd project
   npm start
   ```

2. **Backend should show**:
   ```
   ✅ Local MongoDB connected successfully!
   🌐 Using local database: mindease_local
   ```

3. **Your Flutter app will now connect to real MongoDB instead of mock data**

## 📞 Need Help?

If you're still having issues:

1. **Check Windows Services**:
   - Press `Win + R`, type `services.msc`
   - Look for "MongoDB" service
   - Right-click → Start

2. **Check Event Viewer**:
   - Press `Win + R`, type `eventvwr.msc`
   - Look for MongoDB errors in Application logs

3. **Firewall Issues**:
   - Windows Defender might be blocking port 27017
   - Add exception for MongoDB

## 🎯 Quick Commands Summary

```cmd
# Start MongoDB service
net start MongoDB

# Stop MongoDB service  
net stop MongoDB

# Check if running
netstat -an | findstr 27017

# Manual start
mongod --dbpath "C:\data\db"

# Test connection
mongo --eval "db.runCommand({ping:1})"
```

## 🔗 Useful Links

- [MongoDB Community Download](https://www.mongodb.com/try/download/community)
- [MongoDB Installation Guide](https://docs.mongodb.com/manual/tutorial/install-mongodb-on-windows/)
- [MongoDB Compass Guide](https://docs.mongodb.com/compass/current/)

---

**Once MongoDB is running, your MindEase app will have full database functionality!** 🎉
