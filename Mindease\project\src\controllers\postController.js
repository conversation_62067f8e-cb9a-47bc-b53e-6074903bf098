const firebaseService = require('../services/firebaseService');

class PostController {
  static async getAllPosts(req, res, next) {
    try {
      await firebaseService.initialize();
      const moodQuery = await firebaseService.db.collection('moodEntries').get();
      const moods = moodQuery.docs ?
        moodQuery.docs.map(doc => ({ id: doc.id, ...doc.data() })) :
        moodQuery || [];

      // Sort by date (newest first)
      moods.sort((a, b) => {
        const dateA = new Date(a.date || a.createdAt);
        const dateB = new Date(b.date || b.createdAt);
        return dateB - dateA;
      });

      res.json(moods);
    } catch (error) {
      next(error);
    }
  }

  static async getmoodById(req, res, next) {
    try {
      await firebaseService.initialize();
      const { userId } = req.params;
      console.log('Fetching moods for userId:', userId);

      const moods = await firebaseService.getMoodEntriesByUserId(userId);
      console.log('Found moods:', moods.length);
      res.status(200).json(moods);
    } catch (error) {
      console.error('Error fetching moods:', error);
      next(error);
    }
  }

  static async moodentry(req, res, next) {
    try {
      await firebaseService.initialize();
      const { userId, mood, note, intensity, notes } = req.body;

      if (!userId || !mood) {
        return res.status(400).json({ error: 'userId and mood are required.' });
      }

      const moodData = {
        userId,
        mood,
        note: note || notes,
        intensity: intensity || 5,
        date: new Date()
      };

      const newMood = await firebaseService.createMoodEntry(moodData);
      res.status(201).json(newMood);
    } catch (error) {
      next(error);
    }
  }

  static async deletePost(req, res, next) {
    try {
      const { id } = req.params;
      const mood = await Mood.findByIdAndDelete(id);
      if (!mood) {
        return res.status(404).json({ error: 'Mood entry not found.' });
      }
      res.json({ message: `Mood entry ${id} deleted` });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = PostController;