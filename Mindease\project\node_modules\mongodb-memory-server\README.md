# mongodb-memory-server

[![Node.js CI](https://github.com/typegoose/mongodb-memory-server/workflows/Node.js%20CI/badge.svg)](https://github.com/typegoose/mongodb-memory-server/actions/workflows/tests.yml?query=workflow%3A%22Node.js+CI%22)
[![NPM version](https://img.shields.io/npm/v/mongodb-memory-server.svg)](https://www.npmjs.com/package/mongodb-memory-server)
[![Downloads stat](https://img.shields.io/npm/dt/mongodb-memory-server.svg)](http://www.npmtrends.com/mongodb-memory-server)
[![Commitizen friendly](https://img.shields.io/badge/commitizen-friendly-brightgreen.svg)](http://commitizen.github.io/cz-cli/)
[![TypeScript compatible](https://img.shields.io/badge/typescript-compatible-brightgreen.svg)](https://www.typescriptlang.org)
[![codecov.io](https://codecov.io/github/typegoose/mongodb-memory-server/coverage.svg?branch=master)](https://codecov.io/github/typegoose/mongodb-memory-server?branch=master)
[![Backers on Open Collective](https://opencollective.com/mongodb-memory-server/backers/badge.svg)](#backers)
[![Sponsors on Open Collective](https://opencollective.com/mongodb-memory-server/sponsors/badge.svg)](#sponsors)
[![mongodb-memory-server](https://snyk.io/advisor/npm-package/mongodb-memory-server/badge.svg)](https://snyk.io/advisor/npm-package/mongodb-memory-server)

Main default package which downloads mongod binary to `./node_modules/.cache` directory on package install.

[Full README with avaliable options and examples](https://github.com/typegoose/mongodb-memory-server)
