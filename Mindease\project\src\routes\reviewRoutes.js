const express = require('express');
const router = express.Router();
const firebaseService = require('../services/firebaseService');

// Get all reviews
router.get('/', async (req, res, next) => {
  try {
    await firebaseService.initialize();
    const reviewsQuery = await firebaseService.db.collection('reviews').get();
    const reviews = reviewsQuery.docs ? 
      reviewsQuery.docs.map(doc => ({ id: doc.id, ...doc.data() })) :
      reviewsQuery || [];
    res.json(reviews);
  } catch (error) {
    next(error);
  }
});

// Get reviews by therapist ID
router.get('/therapist/:therapistId', async (req, res, next) => {
  try {
    await firebaseService.initialize();
    const reviews = await firebaseService.getReviewsByTherapistId(req.params.therapistId);
    res.json(reviews);
  } catch (error) {
    next(error);
  }
});

// Create a new review
router.post('/', async (req, res, next) => {
  try {
    await firebaseService.initialize();
    const review = await firebaseService.createReview(req.body);
    res.status(201).json(review);
  } catch (error) {
    next(error);
  }
});

module.exports = router;
