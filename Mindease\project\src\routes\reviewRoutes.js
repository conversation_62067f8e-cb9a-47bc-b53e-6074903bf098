const express = require('express');
const router = express.Router();
const { Review } = require('../models');

// Get all reviews
router.get('/', async (req, res, next) => {
  try {
    const reviews = await Review.find().sort({ createdAt: -1 });
    res.json(reviews);
  } catch (error) {
    next(error);
  }
});

// Get reviews by therapist ID
router.get('/therapist/:therapistId', async (req, res, next) => {
  try {
    const reviews = await Review.find({ therapistId: req.params.therapistId }).sort({ createdAt: -1 });
    res.json(reviews);
  } catch (error) {
    next(error);
  }
});

// Create a new review
router.post('/', async (req, res, next) => {
  try {
    const review = new Review(req.body);
    await review.save();
    res.status(201).json(review);
  } catch (error) {
    next(error);
  }
});

module.exports = router;
