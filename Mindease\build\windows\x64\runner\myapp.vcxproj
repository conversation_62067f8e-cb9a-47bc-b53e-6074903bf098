﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{24A90C85-635E-3F3D-91DF-788D014E9FDE}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>myapp</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Downloads\Mindease\Mindease\build\windows\x64\runner\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">myapp.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">myapp</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">C:\Users\<USER>\Downloads\Mindease\Mindease\build\windows\x64\runner\Profile\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">myapp.dir\Profile\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">myapp</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Downloads\Mindease\Mindease\build\windows\x64\runner\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">myapp.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">myapp</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Downloads\Mindease\Mindease\windows;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_HAS_EXCEPTIONS=0;_DEBUG;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Downloads\Mindease\Mindease\windows;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Downloads\Mindease\Mindease\windows;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Debug\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\file_selector_windows\Debug\file_selector_windows_plugin.lib;..\plugins\flutter_secure_storage_windows\Debug\flutter_secure_storage_windows_plugin.lib;..\plugins\geolocator_windows\Debug\geolocator_windows_plugin.lib;..\plugins\permission_handler_windows\Debug\permission_handler_windows_plugin.lib;..\plugins\url_launcher_windows\Debug\url_launcher_windows_plugin.lib;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Downloads/Mindease/Mindease/build/windows/x64/runner/Debug/myapp.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Downloads/Mindease/Mindease/build/windows/x64/runner/Debug/myapp.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>C:\Users\<USER>\Downloads\Mindease\Mindease\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Downloads\Mindease\Mindease\windows;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Profile"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Profile\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Downloads\Mindease\Mindease\windows;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Downloads\Mindease\Mindease\windows;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Profile\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\file_selector_windows\Profile\file_selector_windows_plugin.lib;..\plugins\flutter_secure_storage_windows\Profile\flutter_secure_storage_windows_plugin.lib;..\plugins\geolocator_windows\Profile\geolocator_windows_plugin.lib;..\plugins\permission_handler_windows\Profile\permission_handler_windows_plugin.lib;..\plugins\url_launcher_windows\Profile\url_launcher_windows_plugin.lib;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Downloads/Mindease/Mindease/build/windows/x64/runner/Profile/myapp.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Downloads/Mindease/Mindease/build/windows/x64/runner/Profile/myapp.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>C:\Users\<USER>\Downloads\Mindease\Mindease\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Downloads\Mindease\Mindease\windows;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Downloads\Mindease\Mindease\windows;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Downloads\Mindease\Mindease\windows;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\flutter_secure_storage_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Release\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\file_selector_windows\Release\file_selector_windows_plugin.lib;..\plugins\flutter_secure_storage_windows\Release\flutter_secure_storage_windows_plugin.lib;..\plugins\geolocator_windows\Release\geolocator_windows_plugin.lib;..\plugins\permission_handler_windows\Release\permission_handler_windows_plugin.lib;..\plugins\url_launcher_windows\Release\url_launcher_windows_plugin.lib;C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Downloads/Mindease/Mindease/build/windows/x64/runner/Release/myapp.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Downloads/Mindease/Mindease/build/windows/x64/runner/Release/myapp.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>C:\Users\<USER>\Downloads\Mindease\Mindease\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Downloads\Mindease\Mindease\windows\runner\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Downloads/Mindease/Mindease/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Downloads/Mindease/Mindease/windows -BC:/Users/<USER>/Downloads/Mindease/Mindease/build/windows/x64 --check-stamp-file C:/Users/<USER>/Downloads/Mindease/Mindease/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Downloads\Mindease\Mindease\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Building Custom Rule C:/Users/<USER>/Downloads/Mindease/Mindease/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Downloads/Mindease/Mindease/windows -BC:/Users/<USER>/Downloads/Mindease/Mindease/build/windows/x64 --check-stamp-file C:/Users/<USER>/Downloads/Mindease/Mindease/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">C:\Users\<USER>\Downloads\Mindease\Mindease\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Downloads/Mindease/Mindease/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Downloads/Mindease/Mindease/windows -BC:/Users/<USER>/Downloads/Mindease/Mindease/build/windows/x64 --check-stamp-file C:/Users/<USER>/Downloads/Mindease/Mindease/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Downloads\Mindease\Mindease\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Downloads\Mindease\Mindease\windows\runner\flutter_window.cpp" />
    <ClCompile Include="C:\Users\<USER>\Downloads\Mindease\Mindease\windows\runner\main.cpp" />
    <ClCompile Include="C:\Users\<USER>\Downloads\Mindease\Mindease\windows\runner\utils.cpp" />
    <ClCompile Include="C:\Users\<USER>\Downloads\Mindease\Mindease\windows\runner\win32_window.cpp" />
    <ClCompile Include="C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\generated_plugin_registrant.cc" />
    <ResourceCompile Include="C:\Users\<USER>\Downloads\Mindease\Mindease\windows\runner\Runner.rc" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Downloads\Mindease\Mindease\build\windows\x64\ZERO_CHECK.vcxproj">
      <Project>{B1C7D9D8-4601-341B-B70E-3804349E1773}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Downloads\Mindease\Mindease\build\windows\x64\plugins\file_selector_windows\file_selector_windows_plugin.vcxproj">
      <Project>{F9BDF8BD-F459-3FF8-96C3-A1E48F409A82}</Project>
      <Name>file_selector_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Downloads\Mindease\Mindease\build\windows\x64\flutter\flutter_assemble.vcxproj">
      <Project>{29DA3138-74D1-3CF0-9209-17CF90A8FFB6}</Project>
      <Name>flutter_assemble</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Downloads\Mindease\Mindease\build\windows\x64\plugins\flutter_secure_storage_windows\flutter_secure_storage_windows_plugin.vcxproj">
      <Project>{64940E29-491D-36CF-8EDB-0CC085A2A982}</Project>
      <Name>flutter_secure_storage_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Downloads\Mindease\Mindease\build\windows\x64\flutter\flutter_wrapper_app.vcxproj">
      <Project>{A626A568-5710-307C-8876-1F1C20801ECC}</Project>
      <Name>flutter_wrapper_app</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Downloads\Mindease\Mindease\build\windows\x64\plugins\geolocator_windows\geolocator_windows_plugin.vcxproj">
      <Project>{CC226E52-CFA3-3345-A30B-CACFDBF609FA}</Project>
      <Name>geolocator_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Downloads\Mindease\Mindease\build\windows\x64\plugins\permission_handler_windows\permission_handler_windows_plugin.vcxproj">
      <Project>{51647267-4C34-3C9C-9131-229D1BCEA266}</Project>
      <Name>permission_handler_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Downloads\Mindease\Mindease\build\windows\x64\plugins\url_launcher_windows\url_launcher_windows_plugin.vcxproj">
      <Project>{********-DE05-358F-97EE-5C7DC52B5E4D}</Project>
      <Name>url_launcher_windows_plugin</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>