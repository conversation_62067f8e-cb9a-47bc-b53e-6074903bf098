﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{D3E6EAB5-D928-3BD1-A153-7A33CA4CB421}"
	ProjectSection(ProjectDependencies) = postProject
		{B1C7D9D8-4601-341B-B70E-3804349E1773} = {B1C7D9D8-4601-341B-B70E-3804349E1773}
		{CC226E52-CFA3-3345-A30B-CACFDBF609FA} = {CC226E52-CFA3-3345-A30B-CACFDBF609FA}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{47931453-803C-3686-BEAF-7DA48C0CBC41}"
	ProjectSection(ProjectDependencies) = postProject
		{D3E6EAB5-D928-3BD1-A153-7A33CA4CB421} = {D3E6EAB5-D928-3BD1-A153-7A33CA4CB421}
		{B1C7D9D8-4601-341B-B70E-3804349E1773} = {B1C7D9D8-4601-341B-B70E-3804349E1773}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{B1C7D9D8-4601-341B-B70E-3804349E1773}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\..\flutter\flutter_assemble.vcxproj", "{29DA3138-74D1-3CF0-9209-17CF90A8FFB6}"
	ProjectSection(ProjectDependencies) = postProject
		{B1C7D9D8-4601-341B-B70E-3804349E1773} = {B1C7D9D8-4601-341B-B70E-3804349E1773}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\..\flutter\flutter_wrapper_plugin.vcxproj", "{BA466A9A-EF84-3DEE-AB54-8E23577CBE4D}"
	ProjectSection(ProjectDependencies) = postProject
		{B1C7D9D8-4601-341B-B70E-3804349E1773} = {B1C7D9D8-4601-341B-B70E-3804349E1773}
		{29DA3138-74D1-3CF0-9209-17CF90A8FFB6} = {29DA3138-74D1-3CF0-9209-17CF90A8FFB6}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "geolocator_windows_plugin", "geolocator_windows_plugin.vcxproj", "{CC226E52-CFA3-3345-A30B-CACFDBF609FA}"
	ProjectSection(ProjectDependencies) = postProject
		{B1C7D9D8-4601-341B-B70E-3804349E1773} = {B1C7D9D8-4601-341B-B70E-3804349E1773}
		{29DA3138-74D1-3CF0-9209-17CF90A8FFB6} = {29DA3138-74D1-3CF0-9209-17CF90A8FFB6}
		{BA466A9A-EF84-3DEE-AB54-8E23577CBE4D} = {BA466A9A-EF84-3DEE-AB54-8E23577CBE4D}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{D3E6EAB5-D928-3BD1-A153-7A33CA4CB421}.Debug|x64.ActiveCfg = Debug|x64
		{D3E6EAB5-D928-3BD1-A153-7A33CA4CB421}.Debug|x64.Build.0 = Debug|x64
		{D3E6EAB5-D928-3BD1-A153-7A33CA4CB421}.Profile|x64.ActiveCfg = Profile|x64
		{D3E6EAB5-D928-3BD1-A153-7A33CA4CB421}.Profile|x64.Build.0 = Profile|x64
		{D3E6EAB5-D928-3BD1-A153-7A33CA4CB421}.Release|x64.ActiveCfg = Release|x64
		{D3E6EAB5-D928-3BD1-A153-7A33CA4CB421}.Release|x64.Build.0 = Release|x64
		{47931453-803C-3686-BEAF-7DA48C0CBC41}.Debug|x64.ActiveCfg = Debug|x64
		{47931453-803C-3686-BEAF-7DA48C0CBC41}.Profile|x64.ActiveCfg = Profile|x64
		{47931453-803C-3686-BEAF-7DA48C0CBC41}.Release|x64.ActiveCfg = Release|x64
		{B1C7D9D8-4601-341B-B70E-3804349E1773}.Debug|x64.ActiveCfg = Debug|x64
		{B1C7D9D8-4601-341B-B70E-3804349E1773}.Debug|x64.Build.0 = Debug|x64
		{B1C7D9D8-4601-341B-B70E-3804349E1773}.Profile|x64.ActiveCfg = Profile|x64
		{B1C7D9D8-4601-341B-B70E-3804349E1773}.Profile|x64.Build.0 = Profile|x64
		{B1C7D9D8-4601-341B-B70E-3804349E1773}.Release|x64.ActiveCfg = Release|x64
		{B1C7D9D8-4601-341B-B70E-3804349E1773}.Release|x64.Build.0 = Release|x64
		{29DA3138-74D1-3CF0-9209-17CF90A8FFB6}.Debug|x64.ActiveCfg = Debug|x64
		{29DA3138-74D1-3CF0-9209-17CF90A8FFB6}.Debug|x64.Build.0 = Debug|x64
		{29DA3138-74D1-3CF0-9209-17CF90A8FFB6}.Profile|x64.ActiveCfg = Profile|x64
		{29DA3138-74D1-3CF0-9209-17CF90A8FFB6}.Profile|x64.Build.0 = Profile|x64
		{29DA3138-74D1-3CF0-9209-17CF90A8FFB6}.Release|x64.ActiveCfg = Release|x64
		{29DA3138-74D1-3CF0-9209-17CF90A8FFB6}.Release|x64.Build.0 = Release|x64
		{BA466A9A-EF84-3DEE-AB54-8E23577CBE4D}.Debug|x64.ActiveCfg = Debug|x64
		{BA466A9A-EF84-3DEE-AB54-8E23577CBE4D}.Debug|x64.Build.0 = Debug|x64
		{BA466A9A-EF84-3DEE-AB54-8E23577CBE4D}.Profile|x64.ActiveCfg = Profile|x64
		{BA466A9A-EF84-3DEE-AB54-8E23577CBE4D}.Profile|x64.Build.0 = Profile|x64
		{BA466A9A-EF84-3DEE-AB54-8E23577CBE4D}.Release|x64.ActiveCfg = Release|x64
		{BA466A9A-EF84-3DEE-AB54-8E23577CBE4D}.Release|x64.Build.0 = Release|x64
		{CC226E52-CFA3-3345-A30B-CACFDBF609FA}.Debug|x64.ActiveCfg = Debug|x64
		{CC226E52-CFA3-3345-A30B-CACFDBF609FA}.Debug|x64.Build.0 = Debug|x64
		{CC226E52-CFA3-3345-A30B-CACFDBF609FA}.Profile|x64.ActiveCfg = Profile|x64
		{CC226E52-CFA3-3345-A30B-CACFDBF609FA}.Profile|x64.Build.0 = Profile|x64
		{CC226E52-CFA3-3345-A30B-CACFDBF609FA}.Release|x64.ActiveCfg = Release|x64
		{CC226E52-CFA3-3345-A30B-CACFDBF609FA}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {45DB70C7-3877-3BC6-B14E-3204E588C90D}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
