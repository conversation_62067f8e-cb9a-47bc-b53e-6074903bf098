import 'package:flutter/material.dart';
import 'package:myapp/theme/app_theme.dart';
import 'package:myapp/components/modern_card_widgets.dart';
import 'package:myapp/components/modern_navigation.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:fl_chart/fl_chart.dart';

class AnalysisScreen extends StatefulWidget {
  final String userId;
  
  const AnalysisScreen({
    super.key,
    required this.userId,
  });

  @override
  State<AnalysisScreen> createState() => _AnalysisScreenState();
}

class _AnalysisScreenState extends State<AnalysisScreen>
    with TickerProviderStateMixin {
  bool isLoading = true;
  Map<String, dynamic> analysisData = {};
  late TabController tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    tabController = TabController(length: 3, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _fetchAnalysisData();
  }

  @override
  void dispose() {
    tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _fetchAnalysisData() async {
    setState(() {
      isLoading = true;
    });

    try {
      final response = await http.get(
        Uri.parse('http://localhost:3000/api/analysis/${widget.userId}'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        setState(() {
          analysisData = jsonDecode(response.body);
          isLoading = false;
        });
        _animationController.forward();
      } else {
        // Use mock data if API fails
        _loadMockData();
      }
    } catch (e) {
      _loadMockData();
    }
  }

  void _loadMockData() {
    setState(() {
      analysisData = {
        'moodScore': 7.2,
        'weeklyProgress': 15.5,
        'totalSessions': 12,
        'journalEntries': 28,
        'moodTrend': 'improving',
        'weeklyMoodData': [6.5, 7.0, 6.8, 7.2, 7.5, 7.1, 7.3],
        'moodDistribution': {
          'excellent': 25,
          'good': 35,
          'okay': 25,
          'poor': 10,
          'terrible': 5,
        },
        'insights': [
          'Your mood has improved by 15% this week',
          'You\'ve been consistent with journaling',
          'Consider scheduling more therapy sessions',
        ],
      };
      isLoading = false;
    });
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Container(
        decoration: BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              ModernAppBar(
                title: 'Your Analysis',
                subtitle: 'Track your mental wellness journey',
                actions: [
                  IconButton(
                    onPressed: _fetchAnalysisData,
                    icon: const Icon(Icons.refresh, color: Colors.white),
                  ),
                ],
              ),
              ModernTabBar(
                controller: tabController,
                tabs: const ['Overview', 'Trends', 'Insights'],
              ),
              Expanded(
                child: isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : FadeTransition(
                        opacity: _fadeAnimation,
                        child: TabBarView(
                          controller: tabController,
                          children: [
                            _buildOverviewTab(),
                            _buildTrendsTab(),
                            _buildInsightsTab(),
                          ],
                        ),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingL),
      child: Column(
        children: [
          // Stats Cards Row
          Row(
            children: [
              Expanded(
                child: ModernStatsCard(
                  title: 'Mood Score',
                  value: '${analysisData['moodScore'] ?? 0}/10',
                  icon: Icons.sentiment_satisfied,
                  color: AppTheme.primaryColor,
                  subtitle: 'This week',
                ),
              ),
              const SizedBox(width: AppTheme.spacingM),
              Expanded(
                child: ModernStatsCard(
                  title: 'Progress',
                  value: '+${analysisData['weeklyProgress'] ?? 0}%',
                  icon: Icons.trending_up,
                  color: AppTheme.successColor,
                  subtitle: 'vs last week',
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingL),
          
          // Second Row
          Row(
            children: [
              Expanded(
                child: ModernStatsCard(
                  title: 'Sessions',
                  value: '${analysisData['totalSessions'] ?? 0}',
                  icon: Icons.psychology,
                  color: AppTheme.secondaryColor,
                  subtitle: 'Total completed',
                ),
              ),
              const SizedBox(width: AppTheme.spacingM),
              Expanded(
                child: ModernStatsCard(
                  title: 'Journal Entries',
                  value: '${analysisData['journalEntries'] ?? 0}',
                  icon: Icons.book,
                  color: AppTheme.accentColor,
                  subtitle: 'This month',
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingL),

          // Weekly Mood Chart
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingL),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(AppTheme.radiusL),
              boxShadow: AppTheme.softShadow,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Weekly Mood Trend',
                  style: AppTheme.headingMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingL),
                SizedBox(
                  height: 200,
                  child: LineChart(
                    LineChartData(
                      gridData: FlGridData(show: false),
                      titlesData: FlTitlesData(
                        leftTitles: AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                        rightTitles: AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                        topTitles: AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                        bottomTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            getTitlesWidget: (value, meta) {
                              const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
                              if (value.toInt() >= 0 && value.toInt() < days.length) {
                                return Text(
                                  days[value.toInt()],
                                  style: AppTheme.bodySmall.copyWith(
                                    color: AppTheme.textLight,
                                  ),
                                );
                              }
                              return const Text('');
                            },
                          ),
                        ),
                      ),
                      borderData: FlBorderData(show: false),
                      lineBarsData: [
                        LineChartBarData(
                          spots: _getMoodSpots(),
                          isCurved: true,
                          color: AppTheme.primaryColor,
                          barWidth: 3,
                          dotData: FlDotData(
                            show: true,
                            getDotPainter: (spot, percent, barData, index) {
                              return FlDotCirclePainter(
                                radius: 4,
                                color: AppTheme.primaryColor,
                                strokeWidth: 2,
                                strokeColor: Colors.white,
                              );
                            },
                          ),
                          belowBarData: BarAreaData(
                            show: true,
                            color: AppTheme.primaryColor.withOpacity(0.1),
                          ),
                        ),
                      ],
                      minY: 0,
                      maxY: 10,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrendsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingL),
      child: Column(
        children: [
          // Mood Distribution Chart
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingL),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(AppTheme.radiusL),
              boxShadow: AppTheme.softShadow,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Mood Distribution',
                  style: AppTheme.headingMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingL),
                SizedBox(
                  height: 200,
                  child: PieChart(
                    PieChartData(
                      sections: _getMoodSections(),
                      centerSpaceRadius: 40,
                      sectionsSpace: 2,
                    ),
                  ),
                ),
                const SizedBox(height: AppTheme.spacingL),
                _buildMoodLegend(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInsightsTab() {
    final insights = analysisData['insights'] as List<dynamic>? ?? [];
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingL),
      child: Column(
        children: [
          ...insights.asMap().entries.map((entry) {
            final index = entry.key;
            final insight = entry.value;
            
            return Container(
              margin: const EdgeInsets.only(bottom: AppTheme.spacingL),
              padding: const EdgeInsets.all(AppTheme.spacingL),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(AppTheme.radiusL),
                boxShadow: AppTheme.softShadow,
              ),
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppTheme.radiusM),
                    ),
                    child: Icon(
                      Icons.lightbulb_outline,
                      color: AppTheme.primaryColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: AppTheme.spacingM),
                  Expanded(
                    child: Text(
                      insight.toString(),
                      style: AppTheme.bodyLarge.copyWith(
                        color: AppTheme.textPrimary,
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  List<FlSpot> _getMoodSpots() {
    final weeklyData = analysisData['weeklyMoodData'] as List<dynamic>? ?? [7, 7, 7, 7, 7, 7, 7];
    return weeklyData.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.toDouble());
    }).toList();
  }

  List<PieChartSectionData> _getMoodSections() {
    final distribution = analysisData['moodDistribution'] as Map<String, dynamic>? ?? {};
    final colors = [
      AppTheme.successColor,
      AppTheme.primaryColor,
      AppTheme.warningColor,
      AppTheme.accentColor,
      AppTheme.errorColor,
    ];
    
    return distribution.entries.toList().asMap().entries.map((entry) {
      final index = entry.key;
      final data = entry.value;
      return PieChartSectionData(
        value: data.value.toDouble(),
        title: '${data.value}%',
        color: colors[index % colors.length],
        radius: 60,
        titleStyle: AppTheme.bodyMedium.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      );
    }).toList();
  }

  Widget _buildMoodLegend() {
    final distribution = analysisData['moodDistribution'] as Map<String, dynamic>? ?? {};
    final colors = [
      AppTheme.successColor,
      AppTheme.primaryColor,
      AppTheme.warningColor,
      AppTheme.accentColor,
      AppTheme.errorColor,
    ];
    
    return Wrap(
      spacing: AppTheme.spacingM,
      runSpacing: AppTheme.spacingS,
      children: distribution.entries.toList().asMap().entries.map((entry) {
        final index = entry.key;
        final data = entry.value;
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: colors[index % colors.length],
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              data.key.toString().toUpperCase(),
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        );
      }).toList(),
    );
  }
}
