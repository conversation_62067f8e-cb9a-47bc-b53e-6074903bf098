^C:\USERS\<USER>\DOWNLOADS\MINDEASE\MINDEASE\BUILD\WINDOWS\X64\CMAKEFILES\19F966F3B97E88F08067CA0B5AF60C90\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Downloads/Mindease/Mindease/windows -BC:/Users/<USER>/Downloads/Mindease/Mindease/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/Downloads/Mindease/Mindease/build/windows/x64/myapp.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
