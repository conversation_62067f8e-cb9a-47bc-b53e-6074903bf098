import 'package:flutter/material.dart';
import 'package:myapp/theme/app_theme.dart';
import 'package:myapp/login_page.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen>
    with TickerProviderStateMixin {
  PageController _pageController = PageController();
  int currentIndex = 0;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  final List<OnboardingData> onboardingData = [
    OnboardingData(
      title: 'Welcome to MindEase',
      description:
          'Your journey to mental wellness starts here. Track your mood, connect with professionals, and find inner peace.',
      icon: Icons.psychology_outlined,
      gradient: AppTheme.primaryGradient,
      accentColor: AppTheme.primaryColor,
    ),
    OnboardingData(
      title: 'Connect with Therapists',
      description:
          'Book sessions with certified mental health professionals. Get the support you need, when you need it.',
      icon: Icons.people_outline,
      gradient: AppTheme.calmGradient,
      accentColor: AppTheme.secondaryColor,
    ),
    OnboardingData(
      title: 'Track Your Progress',
      description:
          'Monitor your mood, journal your thoughts, and see your mental health journey unfold with insights.',
      icon: Icons.trending_up_outlined,
      gradient: AppTheme.healingGradient,
      accentColor: AppTheme.accentColor,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // App Logo and Title Header
              Padding(
                padding: const EdgeInsets.all(AppTheme.spacingL),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // App Logo with Theme Colors
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        gradient: AppTheme.primaryGradient,
                        borderRadius: BorderRadius.circular(AppTheme.radiusL),
                        boxShadow: AppTheme.mediumShadow,
                      ),
                      child: const Icon(
                        Icons.psychology,
                        color: Colors.white,
                        size: 32,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingM),
                    // App Title with Theme Colors
                    Text(
                      'MindEase',
                      style: AppTheme.headingLarge.copyWith(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimary,
                        shadows: [
                          Shadow(
                            offset: const Offset(0, 2),
                            blurRadius: 4,
                            color: Colors.black.withOpacity(0.1),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() {
                      currentIndex = index;
                    });
                    _animationController.reset();
                    _animationController.forward();
                  },
                  itemCount: onboardingData.length,
                  itemBuilder: (context, index) {
                    return OnboardingPage(
                      data: onboardingData[index],
                      fadeAnimation: _fadeAnimation,
                      scaleAnimation: _scaleAnimation,
                    );
                  },
                ),
              ),
              // Bottom Navigation
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingL),
                child: Column(
                  children: [
                    // Page Indicators
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(
                        onboardingData.length,
                        (index) => AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          margin: const EdgeInsets.symmetric(horizontal: 6),
                          width: currentIndex == index ? 32 : 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: currentIndex == index
                                ? AppTheme.primaryColor
                                : AppTheme.textLight.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(6),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingL),
                    // Navigation Buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Skip Button
                        TextButton(
                          onPressed: () {
                            Navigator.pushReplacement(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const LoginPage(),
                              ),
                            );
                          },
                          child: Text(
                            'Skip',
                            style: AppTheme.bodyLarge.copyWith(
                              color: AppTheme.textLight,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        // Next/Get Started Button
                        ElevatedButton(
                          onPressed: () {
                            if (currentIndex == onboardingData.length - 1) {
                              Navigator.pushReplacement(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const LoginPage(),
                                ),
                              );
                            } else {
                              _pageController.nextPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                              );
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 32,
                              vertical: 16,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.circular(AppTheme.radiusL),
                            ),
                            elevation: 2,
                          ),
                          child: Text(
                            currentIndex == onboardingData.length - 1
                                ? 'Get Started'
                                : 'Next',
                            style: AppTheme.buttonText.copyWith(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class OnboardingPage extends StatefulWidget {
  final OnboardingData data;
  final Animation<double> fadeAnimation;
  final Animation<double> scaleAnimation;

  const OnboardingPage({
    super.key,
    required this.data,
    required this.fadeAnimation,
    required this.scaleAnimation,
  });

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.fadeAnimation,
      builder: (context, child) {
        return FadeTransition(
          opacity: widget.fadeAnimation,
          child: ScaleTransition(
            scale: widget.scaleAnimation,
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.spacingL),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Animated Icon Container
                    AnimatedBuilder(
                      animation: _pulseAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _pulseAnimation.value,
                          child: Container(
                            width: 180,
                            height: 180,
                            decoration: BoxDecoration(
                              gradient: widget.data.gradient,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color:
                                      widget.data.accentColor.withOpacity(0.3),
                                  blurRadius: 25,
                                  offset: const Offset(0, 10),
                                  spreadRadius: 2,
                                ),
                              ],
                            ),
                            child: widget.data.icon != null
                                ? Icon(
                                    widget.data.icon!,
                                    size: 80,
                                    color: Colors.white,
                                  )
                                : widget.data.imagePath != null
                                    ? Image.asset(
                                        widget.data.imagePath!,
                                        width: 120,
                                        height: 120,
                                        fit: BoxFit.contain,
                                      )
                                    : const Icon(
                                        Icons.psychology,
                                        size: 80,
                                        color: Colors.white,
                                      ),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 40),
                    // Title
                    Text(
                      widget.data.title,
                      style: AppTheme.headingLarge.copyWith(
                        color: AppTheme.textPrimary,
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        height: 1.2,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: AppTheme.spacingL),
                    // Description
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: AppTheme.spacingL),
                      child: Text(
                        widget.data.description,
                        style: AppTheme.bodyLarge.copyWith(
                          color: AppTheme.textSecondary,
                          height: 1.6,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class OnboardingData {
  final String title;
  final String description;
  final String? imagePath;
  final IconData? icon;
  final LinearGradient gradient;
  final Color accentColor;

  OnboardingData({
    required this.title,
    required this.description,
    this.imagePath,
    this.icon,
    required this.gradient,
    required this.accentColor,
  });
}
