// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Management_Workplace_1_H
#define WINRT_Windows_Management_Workplace_1_H
#include "winrt/impl/Windows.Management.Workplace.0.h"
WINRT_EXPORT namespace winrt::Windows::Management::Workplace
{
    struct __declspec(empty_bases) IMdmAllowPolicyStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMdmAllowPolicyStatics>
    {
        IMdmAllowPolicyStatics(std::nullptr_t = nullptr) noexcept {}
        IMdmAllowPolicyStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMdmPolicyStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMdmPolicyStatics2>
    {
        IMdmPolicyStatics2(std::nullptr_t = nullptr) noexcept {}
        IMdmPolicyStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
