// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Services_Store_1_H
#define WINRT_Windows_Services_Store_1_H
#include "winrt/impl/Windows.Foundation.0.h"
#include "winrt/impl/Windows.Services.Store.0.h"
WINRT_EXPORT namespace winrt::Windows::Services::Store
{
    struct __declspec(empty_bases) IStoreAcquireLicenseResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreAcquireLicenseResult>
    {
        IStoreAcquireLicenseResult(std::nullptr_t = nullptr) noexcept {}
        IStoreAcquireLicenseResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreAppLicense :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreAppLicense>
    {
        IStoreAppLicense(std::nullptr_t = nullptr) noexcept {}
        IStoreAppLicense(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreAppLicense2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreAppLicense2>
    {
        IStoreAppLicense2(std::nullptr_t = nullptr) noexcept {}
        IStoreAppLicense2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreAvailability :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreAvailability>
    {
        IStoreAvailability(std::nullptr_t = nullptr) noexcept {}
        IStoreAvailability(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreCanAcquireLicenseResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreCanAcquireLicenseResult>
    {
        IStoreCanAcquireLicenseResult(std::nullptr_t = nullptr) noexcept {}
        IStoreCanAcquireLicenseResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreCollectionData :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreCollectionData>
    {
        IStoreCollectionData(std::nullptr_t = nullptr) noexcept {}
        IStoreCollectionData(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreConsumableResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreConsumableResult>
    {
        IStoreConsumableResult(std::nullptr_t = nullptr) noexcept {}
        IStoreConsumableResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreContext :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreContext>
    {
        IStoreContext(std::nullptr_t = nullptr) noexcept {}
        IStoreContext(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreContext2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreContext2>
    {
        IStoreContext2(std::nullptr_t = nullptr) noexcept {}
        IStoreContext2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreContext3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreContext3>
    {
        IStoreContext3(std::nullptr_t = nullptr) noexcept {}
        IStoreContext3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreContext4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreContext4>
    {
        IStoreContext4(std::nullptr_t = nullptr) noexcept {}
        IStoreContext4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreContext5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreContext5>
    {
        IStoreContext5(std::nullptr_t = nullptr) noexcept {}
        IStoreContext5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreContextStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreContextStatics>
    {
        IStoreContextStatics(std::nullptr_t = nullptr) noexcept {}
        IStoreContextStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreImage :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreImage>
    {
        IStoreImage(std::nullptr_t = nullptr) noexcept {}
        IStoreImage(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreLicense :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreLicense>
    {
        IStoreLicense(std::nullptr_t = nullptr) noexcept {}
        IStoreLicense(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorePackageInstallOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorePackageInstallOptions>
    {
        IStorePackageInstallOptions(std::nullptr_t = nullptr) noexcept {}
        IStorePackageInstallOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorePackageLicense :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorePackageLicense>,
        impl::require<winrt::Windows::Services::Store::IStorePackageLicense, winrt::Windows::Foundation::IClosable>
    {
        IStorePackageLicense(std::nullptr_t = nullptr) noexcept {}
        IStorePackageLicense(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorePackageUpdate :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorePackageUpdate>
    {
        IStorePackageUpdate(std::nullptr_t = nullptr) noexcept {}
        IStorePackageUpdate(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorePackageUpdateResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorePackageUpdateResult>
    {
        IStorePackageUpdateResult(std::nullptr_t = nullptr) noexcept {}
        IStorePackageUpdateResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorePackageUpdateResult2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorePackageUpdateResult2>
    {
        IStorePackageUpdateResult2(std::nullptr_t = nullptr) noexcept {}
        IStorePackageUpdateResult2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorePrice :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorePrice>
    {
        IStorePrice(std::nullptr_t = nullptr) noexcept {}
        IStorePrice(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorePrice2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorePrice2>
    {
        IStorePrice2(std::nullptr_t = nullptr) noexcept {}
        IStorePrice2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreProduct :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreProduct>
    {
        IStoreProduct(std::nullptr_t = nullptr) noexcept {}
        IStoreProduct(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreProductOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreProductOptions>
    {
        IStoreProductOptions(std::nullptr_t = nullptr) noexcept {}
        IStoreProductOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreProductPagedQueryResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreProductPagedQueryResult>
    {
        IStoreProductPagedQueryResult(std::nullptr_t = nullptr) noexcept {}
        IStoreProductPagedQueryResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreProductQueryResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreProductQueryResult>
    {
        IStoreProductQueryResult(std::nullptr_t = nullptr) noexcept {}
        IStoreProductQueryResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreProductResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreProductResult>
    {
        IStoreProductResult(std::nullptr_t = nullptr) noexcept {}
        IStoreProductResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorePurchaseProperties :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorePurchaseProperties>
    {
        IStorePurchaseProperties(std::nullptr_t = nullptr) noexcept {}
        IStorePurchaseProperties(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorePurchasePropertiesFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorePurchasePropertiesFactory>
    {
        IStorePurchasePropertiesFactory(std::nullptr_t = nullptr) noexcept {}
        IStorePurchasePropertiesFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorePurchaseResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorePurchaseResult>
    {
        IStorePurchaseResult(std::nullptr_t = nullptr) noexcept {}
        IStorePurchaseResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreQueueItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreQueueItem>
    {
        IStoreQueueItem(std::nullptr_t = nullptr) noexcept {}
        IStoreQueueItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreQueueItem2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreQueueItem2>
    {
        IStoreQueueItem2(std::nullptr_t = nullptr) noexcept {}
        IStoreQueueItem2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreQueueItemCompletedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreQueueItemCompletedEventArgs>
    {
        IStoreQueueItemCompletedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IStoreQueueItemCompletedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreQueueItemStatus :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreQueueItemStatus>
    {
        IStoreQueueItemStatus(std::nullptr_t = nullptr) noexcept {}
        IStoreQueueItemStatus(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreRateAndReviewResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreRateAndReviewResult>
    {
        IStoreRateAndReviewResult(std::nullptr_t = nullptr) noexcept {}
        IStoreRateAndReviewResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreRequestHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreRequestHelperStatics>
    {
        IStoreRequestHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IStoreRequestHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreSendRequestResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreSendRequestResult>
    {
        IStoreSendRequestResult(std::nullptr_t = nullptr) noexcept {}
        IStoreSendRequestResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreSendRequestResult2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreSendRequestResult2>
    {
        IStoreSendRequestResult2(std::nullptr_t = nullptr) noexcept {}
        IStoreSendRequestResult2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreSku :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreSku>
    {
        IStoreSku(std::nullptr_t = nullptr) noexcept {}
        IStoreSku(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreSubscriptionInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreSubscriptionInfo>
    {
        IStoreSubscriptionInfo(std::nullptr_t = nullptr) noexcept {}
        IStoreSubscriptionInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreUninstallStorePackageResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreUninstallStorePackageResult>
    {
        IStoreUninstallStorePackageResult(std::nullptr_t = nullptr) noexcept {}
        IStoreUninstallStorePackageResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreVideo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreVideo>
    {
        IStoreVideo(std::nullptr_t = nullptr) noexcept {}
        IStoreVideo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
