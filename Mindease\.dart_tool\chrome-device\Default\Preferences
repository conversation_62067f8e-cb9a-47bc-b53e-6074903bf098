{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "en-US"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "autocomplete": {"retention_policy_last_version": 138}, "autofill": {"last_version_deduped": 138}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 748, "left": 823, "maximized": false, "right": 1339, "top": 30, "work_area_bottom": 768, "work_area_left": 0, "work_area_right": 1366, "work_area_top": 30}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 20555, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "9787e4d2-6ce7-4152-ac7a-39f71bb5a33f", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "138.0.7204.100", "settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.100\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fignfifoniblkonapihmkfakmlgkbkcf": {"account_extension_type": 0, "active_permissions": {"api": ["metricsPrivate", "systemPrivate", "ttsEngine", "offscreen"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"service_worker": "service_worker.js"}, "description": "Component extension providing speech via the Google network text-to-speech service.", "host_permissions": ["https://www.google.com/"], "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5mnqF6oM8Q5tYd7YqL40YL7Keftt4PwydehlNOyNlCiWDM/7SiQYwxYvVHMj1i03z7B5lZXQinrcqhHhoIgcSHK1JrdzVSJxPRVdmV0rJLv0KQgmVwL8p8MfN6SmHs+72xz+1GoRWpd0WlHMil7RzGKJA4Ku+9jxxsXoxes9eeV1hCavkb1dSF+mlQbaNiw7u1hhvc5mmeuEcWjoce8r8B2R4wmnGbuTLfoSchZ6jkasynmOaFxyT4jiYDYgrNtWRTQ/9PuPduJ+uBWVT/o2ZhDK2XcywVwzUfYIXDLDblK+YdZi8w8ZBNvc7hP9/iZr6/eoUpfsLa8qlJgyLBQebwIDAQAB", "manifest_version": 3, "name": "Google Network Speech", "permissions": ["metricsPrivate", "offscreen", "systemPrivate", "ttsEngine"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.100\\resources\\network_speech_synthesis/mv3", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "1.0"}, "serviceworkerevents": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.100\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.100\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "gaia_cookie": {"changed_time": **********.605516, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "ba2c40a0-8452-447b-8872-8cfc9961715d"}}, "history_clusters": {"all_cache": {"all_keywords": {}, "all_timestamp": "*****************"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "PasswordManualFallbackAvailable": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["***********198683", "*****************"], "session_last_active_time": "***********198683", "session_start_time": "***********198683"}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "tHZl1voc3VuSlC1hicDE0o9Z6WLOYccIgHXd7xxjqcCBDmUZhTUDnJ7XHNglFeN/xi2zXBuh5giC2qRo44LqZw=="}, "migrated_user_scripts_toggle": true, "ntp": {"num_personal_suggestions": 2}, "omnibox": {"shown_count_history_scope_promo": 1}, "optimization_guide": {"predictionmodelfetcher": {"last_fetch_attempt": "*****************", "last_fetch_success": "*****************"}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "pinned_tabs": [], "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 6, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"http://localhost,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {"http://localhost:54394,*": {"last_modified": "*****************", "last_visit": "*****************", "setting": 1}}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"http://localhost:54394,*": {"expiration": "*****************", "last_modified": "********779382256", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "13396543962069306", "setting": {"lastEngagementTime": 1.3396543962069282e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "http://localhost:54394,*": {"last_modified": "********040156809", "setting": {"lastEngagementTime": 1.3396544040156778e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 13.499999999999996, "rawScore": 13.499999999999996}}, "http://localhost:60394,*": {"last_modified": "***********783747", "setting": {"lastEngagementTime": 1.3396514716778596e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.8, "rawScore": 4.8}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "permission_actions": {"geolocation": [{"action": 0, "prompt_disposition": 12, "time": "13396543545621948"}]}, "pref_version": 1}, "created_by_version": "138.0.7204.100", "creation_time": "13396496467959542", "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "********040156778", "last_time_obsolete_http_credentials_removed": **********.207347, "last_time_password_store_metrics_reported": **********.2222, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Your Chrome", "one_time_permission_prompts_decided_count": 1, "password_hash_data_list": [], "were_old_google_logins_removed": true}, "protection": {"macs": {"account_values": {"browser": {"show_home_button": "E1A996F6E1946DCE1932A6A99BF4DF037BB30AE1FCB991C8CC1B87E529BFA335"}, "extensions": {"ui": {"developer_mode": "7CFA220A64C8CE38FDE117BFF3716CE0402C0A722AD5D82EAA3509DE40CCA7AD"}}, "homepage": "836249ED1CF24D4257C9B01E667046162BFF6DE1F941467B98EC22FA4C94874F", "homepage_is_newtabpage": "E6EC7470324EC9AB1178A1D36E9EC0CF3699F946C78667BD19F212B6A4372972", "session": {"restore_on_startup": "23E16C578BC9563626D4A2B6387554C4E1224C7E065A53568AFD0AA98970D445", "startup_urls": "2E924C23DF87F8D3EE3DB91451C3928D3368E9B815D8A4700FA997208CF0A5D3"}}, "browser": {"show_home_button": "8345A28766088907318FAA4F7CE167846A75BB851786665A7B37150F2DFAF8A3"}, "default_search_provider_data": {"template_url_data": "86E99566EA4E807FFFA0ACA84083D80357F4C0187BF793FCC6734199980DECA6"}, "enterprise_signin": {"policy_recovery_token": "0027A6CE4E300459F356A36D6EB75F8C7E9E63F846D3060AE3ABFD7008BA708A"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "9E11EC2B1B582435F629DA09D66090F4BC0B5577DF4E1F6D12D1CABAD75856A2", "fignfifoniblkonapihmkfakmlgkbkcf": "C1B2491A4CEDD03662E7E862715142022C1CA537A8EA526E934399D9C6B333D3", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "8D66D131A83CCCD354B76C008BA0A7751CABE000E4EA93DE7063C5A5D23CE358", "nkeimhogjdpnpccoofpliimaahmaaome": "1811ACCB2CE85D8D4607E985B4C851CCC6B4D52BF1256BD78AFEBB4BF4420E69"}, "ui": {"developer_mode": "2C0AA2D0C73A40C9C6347CB224A7DB2BD4ADFEDB19BFF54B7DB7FF23C818BE16"}}, "google": {"services": {"account_id": "D7D5670CA3DAC5E9D0C1FE0DC9AF229A9E413FA9D419185DF4B6573BED6753B8", "last_signed_in_username": "5A8C9A410064B68433659078A78C133A849187ABDC76103F2B4B8EB151BCF61C", "last_username": "8E2E9AA9EBE3B557C3499E2C5AD40CF31989E6DA0AB77FF144DED1182C78608F"}}, "homepage": "590DB8194F003FD1245FA0001B129A88DC83F69F24803B31056C375AD826BBE1", "homepage_is_newtabpage": "3F76FC7882B3A9C3FE1E9B524688AF3A9B69FE280BC679D1A7954AC8D574D26D", "media": {"cdm": {"origin_data": "48B274D72DBBA3A051773BDBC5FD90684FA67E60F254342B0A42A65B67248AB9"}, "storage_id_salt": "039D9C282B9F68378E18E8084AE19CF7F2BFBD906709A03E64220F5EBE162355"}, "module_blocklist_cache_md5_digest": "2416CAC353232C4C3BD6E281656D8CA3F0955A9C4FBE2DCC60D1AAC86C1ED124", "pinned_tabs": "F27AD190706EB10089B42C0781C7E0FF61DEC00CB99E415A79A8EBF8399A883E", "prefs": {"preference_reset_time": "014E2F436360917F669CAD52F0C2D971F99950D8910C935A0B41C0E67887BCC0"}, "safebrowsing": {"incidents_sent": "67079474CC30EB9D8371D0C160EE63C887325C1B0332DB7C3D80A20E41A5B3DF"}, "search_provider_overrides": "03F478DF5C9A7A7989525464F8C42EF76323E8E13CF2CB40E332BBCD35360024", "session": {"restore_on_startup": "4DB3C604C66AA870FF64B2616EDDAEC35025787914C418C3235B0A16F25EBA07", "startup_urls": "849406BF1FCE47D4ED7D17E45D1FD54B9C6AD864D952810114E16FB152F1A239"}}}, "safebrowsing": {"event_timestamps": {}, "hash_real_time_ohttp_expiration_time": "13396755673642068", "hash_real_time_ohttp_key": "2AAgETtEl5cjzDpksr9J2MiGvUsUaS87sxbZ6vEn9Mxqv18ABAABAAI=", "metrics_last_log_time": "13396496469", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQ7N+Yrq6B5hcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEIr4m66ugeYX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13396406399000000", "uma_in_sql_start_time": "13396496468443032"}, "sessions": {"event_log": [{"crashed": false, "time": "13396496468268612", "type": 0}, {"crashed": false, "time": "***********161345", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "********778964364", "type": 2, "window_count": 1}], "session_data_status": 5}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "138"}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[\"pakistan international airlines\",\"wordle hints\",\"jamal musiala\",\"stop killing games petition\",\"indian fighter jet crashes\",\"ilt20\",\"head over heels episode 7 eng sub\",\"youtube monetization policy\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:groupsinfo\":\"ChoIkk4SFQoRVHJlbmRpbmcgc2VhcmNoZXMoCg\\u003d\\u003d\",\"google:suggestdetail\":[{\"zl\":10002},{\"zl\":10002},{\"google:entityinfo\":\"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\\u003d\",\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002}],\"google:suggesteventid\":\"3741521438424686383\",\"google:suggestrelevance\":[1257,1256,1255,1254,1253,1252,1251,1250],\"google:suggestsubtypes\":[[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308]],\"google:suggesttype\":[\"QUERY\",\"QUERY\",\"ENTITY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\"]}]"}}