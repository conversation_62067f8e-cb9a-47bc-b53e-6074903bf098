<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindEase Firebase Data Viewer</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .nav {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        .nav-item {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            background: none;
            font-size: 16px;
            font-weight: 500;
        }
        .nav-item:hover {
            background: #e9ecef;
        }
        .nav-item.active {
            background: #007bff;
            color: white;
        }
        .content {
            padding: 30px;
        }
        .collection {
            display: none;
            animation: fadeIn 0.3s ease;
        }
        .collection.active {
            display: block;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .data-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin: 15px 0;
            padding: 20px;
            transition: all 0.3s ease;
        }
        .data-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .data-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }
        .data-id {
            font-weight: bold;
            color: #007bff;
            font-size: 1.1em;
        }
        .data-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        .data-field {
            background: white;
            padding: 12px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .field-name {
            font-weight: bold;
            color: #495057;
            font-size: 0.9em;
            margin-bottom: 5px;
        }
        .field-value {
            color: #6c757d;
            word-break: break-word;
        }
        .status {
            padding: 20px;
            text-align: center;
            color: #6c757d;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .refresh-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .refresh-btn:hover {
            background: #218838;
            transform: translateY(-1px);
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 MindEase Firebase Data Viewer</h1>
            <p>Real-time database monitoring and management</p>
        </div>
        
        <div class="nav">
            <button class="nav-item active" onclick="showCollection('users')">👥 Users</button>
            <button class="nav-item" onclick="showCollection('therapists')">👨‍⚕️ Therapists</button>
            <button class="nav-item" onclick="showCollection('appointments')">📅 Appointments</button>
            <button class="nav-item" onclick="showCollection('moodEntries')">😊 Mood Entries</button>
            <button class="nav-item" onclick="showCollection('reviews')">⭐ Reviews</button>
        </div>
        
        <div class="content">
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="userCount">-</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="therapistCount">-</div>
                    <div class="stat-label">Therapists</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="appointmentCount">-</div>
                    <div class="stat-label">Appointments</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="moodCount">-</div>
                    <div class="stat-label">Mood Entries</div>
                </div>
            </div>
            
            <button class="refresh-btn" onclick="loadAllData()">🔄 Refresh All Data</button>
            
            <div id="users" class="collection active">
                <div class="status">Loading users...</div>
            </div>
            <div id="therapists" class="collection">
                <div class="status">Loading therapists...</div>
            </div>
            <div id="appointments" class="collection">
                <div class="status">Loading appointments...</div>
            </div>
            <div id="moodEntries" class="collection">
                <div class="status">Loading mood entries...</div>
            </div>
            <div id="reviews" class="collection">
                <div class="status">Loading reviews...</div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api';
        
        function showCollection(collectionName) {
            // Update nav
            document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
            event.target.classList.add('active');
            
            // Update content
            document.querySelectorAll('.collection').forEach(col => col.classList.remove('active'));
            document.getElementById(collectionName).classList.add('active');
            
            // Load data if not already loaded
            loadCollectionData(collectionName);
        }
        
        async function loadCollectionData(collectionName) {
            const container = document.getElementById(collectionName);
            container.innerHTML = '<div class="status"><div class="loading"></div> Loading...</div>';
            
            try {
                let endpoint = '';
                switch(collectionName) {
                    case 'users':
                        endpoint = '/users';
                        break;
                    case 'therapists':
                        endpoint = '/therapists';
                        break;
                    case 'appointments':
                        endpoint = '/appointments';
                        break;
                    case 'moodEntries':
                        endpoint = '/mood';
                        break;
                    case 'reviews':
                        endpoint = '/reviews';
                        break;
                }
                
                const response = await fetch(API_BASE + endpoint);
                const data = await response.json();
                
                if (Array.isArray(data) && data.length > 0) {
                    container.innerHTML = data.map(item => createDataCard(item, collectionName)).join('');
                } else {
                    container.innerHTML = '<div class="status">No data found</div>';
                }
            } catch (error) {
                container.innerHTML = `<div class="status">Error loading data: ${error.message}</div>`;
            }
        }
        
        function createDataCard(item, collectionName) {
            const excludeFields = ['passwordHash'];
            const fields = Object.entries(item)
                .filter(([key]) => !excludeFields.includes(key))
                .map(([key, value]) => {
                    let displayValue = value;
                    if (typeof value === 'object' && value !== null) {
                        displayValue = JSON.stringify(value, null, 2);
                    }
                    if (typeof value === 'boolean') {
                        displayValue = value ? '✅ Yes' : '❌ No';
                    }
                    if (key.includes('Date') || key.includes('At')) {
                        try {
                            displayValue = new Date(value).toLocaleString();
                        } catch (e) {
                            // Keep original value if not a valid date
                        }
                    }
                    
                    return `
                        <div class="data-field">
                            <div class="field-name">${key}</div>
                            <div class="field-value">${displayValue}</div>
                        </div>
                    `;
                }).join('');
            
            return `
                <div class="data-card">
                    <div class="data-header">
                        <div class="data-id">ID: ${item.id || 'N/A'}</div>
                    </div>
                    <div class="data-content">
                        ${fields}
                    </div>
                </div>
            `;
        }
        
        async function loadAllData() {
            const collections = ['users', 'therapists', 'appointments', 'moodEntries', 'reviews'];
            
            for (const collection of collections) {
                try {
                    let endpoint = '';
                    switch(collection) {
                        case 'users':
                            endpoint = '/users';
                            break;
                        case 'therapists':
                            endpoint = '/therapists';
                            break;
                        case 'appointments':
                            endpoint = '/appointments';
                            break;
                        case 'moodEntries':
                            endpoint = '/mood';
                            break;
                        case 'reviews':
                            endpoint = '/reviews';
                            break;
                    }
                    
                    const response = await fetch(API_BASE + endpoint);
                    const data = await response.json();
                    
                    // Update stats
                    const count = Array.isArray(data) ? data.length : 0;
                    const statElement = document.getElementById(collection.replace('Entries', 'Count').replace('s', 'Count'));
                    if (statElement) {
                        statElement.textContent = count;
                    }
                } catch (error) {
                    console.error(`Error loading ${collection}:`, error);
                }
            }
            
            // Reload current collection
            const activeCollection = document.querySelector('.collection.active');
            if (activeCollection) {
                loadCollectionData(activeCollection.id);
            }
        }
        
        // Load initial data
        loadAllData();
        
        // Auto-refresh every 30 seconds
        setInterval(loadAllData, 30000);
    </script>
</body>
</html>
