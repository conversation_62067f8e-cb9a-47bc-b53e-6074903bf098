@echo off
echo.
echo ========================================
echo   MongoDB Setup for MindEase App
echo ========================================
echo.

REM Create data directory
if not exist "C:\data\db" (
    echo Creating MongoDB data directory...
    mkdir "C:\data\db" 2>nul
)

REM Check if MongoDB is already running
echo Checking if MongoDB is running...
netstat -an | find "27017" >nul
if %errorlevel% == 0 (
    echo.
    echo ✅ MongoDB is already running on port 27017!
    echo 🔗 Connection String: mongodb://localhost:27017
    echo.
    echo 🧭 MongoDB Compass Instructions:
    echo    1. Open MongoDB Compass
    echo    2. Use connection string: mongodb://localhost:27017
    echo    3. Click Connect
    echo.
    pause
    exit /b 0
)

REM Try to start MongoDB service
echo Attempting to start MongoDB service...
net start MongoDB >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ MongoDB service started successfully!
    goto :success
)

REM Try different MongoDB paths
echo Looking for MongoDB installation...

set "MONGO_PATHS="C:\Program Files\MongoDB\Server\7.0\bin\mongod.exe" "C:\Program Files\MongoDB\Server\6.0\bin\mongod.exe" "C:\Program Files\MongoDB\Server\5.0\bin\mongod.exe" "C:\Program Files\MongoDB\Server\4.4\bin\mongod.exe""

for %%i in (%MONGO_PATHS%) do (
    if exist %%i (
        echo Found MongoDB at: %%i
        echo Starting MongoDB server...
        start "MongoDB Server" %%i --dbpath "C:\data\db"
        goto :wait_for_start
    )
)

REM Try mongod from PATH
echo Trying mongod from PATH...
mongod --version >nul 2>&1
if %errorlevel% == 0 (
    echo Starting MongoDB from PATH...
    start "MongoDB Server" mongod --dbpath "C:\data\db"
    goto :wait_for_start
)

REM MongoDB not found
echo.
echo ❌ MongoDB not found!
echo.
echo 📥 Please install MongoDB Community Server:
echo    https://www.mongodb.com/try/download/community
echo.
echo 💡 Installation steps:
echo    1. Download MongoDB Community Server
echo    2. Install with default settings
echo    3. Choose "Install MongoDB as a Service"
echo    4. Run this script again
echo.
pause
exit /b 1

:wait_for_start
echo.
echo ⏳ Waiting for MongoDB to start...
timeout /t 5 /nobreak >nul

REM Check if MongoDB started
for /L %%i in (1,1,10) do (
    netstat -an | find "27017" >nul
    if !errorlevel! == 0 goto :success
    echo Waiting... (%%i/10)
    timeout /t 2 /nobreak >nul
)

echo.
echo ❌ MongoDB may not have started properly
echo 💡 Try running as Administrator or check if port 27017 is available
echo.
pause
exit /b 1

:success
echo.
echo ✅ MongoDB is now running!
echo 🔗 Connection String: mongodb://localhost:27017
echo.
echo 🧭 MongoDB Compass Instructions:
echo    1. Open MongoDB Compass
echo    2. Use connection string: mongodb://localhost:27017
echo    3. Click Connect
echo.
echo 🚀 Your Node.js app can now connect to MongoDB!
echo.
echo Press any key to continue...
pause >nul
