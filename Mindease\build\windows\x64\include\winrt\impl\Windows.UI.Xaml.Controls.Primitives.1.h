// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_UI_Xaml_Controls_Primitives_1_H
#define WINRT_Windows_UI_Xaml_Controls_Primitives_1_H
#include "winrt/impl/Windows.UI.Xaml.Controls.Primitives.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Controls::Primitives
{
    struct __declspec(empty_bases) IAppBarButtonTemplateSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarButtonTemplateSettings>
    {
        IAppBarButtonTemplateSettings(std::nullptr_t = nullptr) noexcept {}
        IAppBarButtonTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarTemplateSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarTemplateSettings>
    {
        IAppBarTemplateSettings(std::nullptr_t = nullptr) noexcept {}
        IAppBarTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarTemplateSettings2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarTemplateSettings2>
    {
        IAppBarTemplateSettings2(std::nullptr_t = nullptr) noexcept {}
        IAppBarTemplateSettings2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarToggleButtonTemplateSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarToggleButtonTemplateSettings>
    {
        IAppBarToggleButtonTemplateSettings(std::nullptr_t = nullptr) noexcept {}
        IAppBarToggleButtonTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IButtonBase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IButtonBase>
    {
        IButtonBase(std::nullptr_t = nullptr) noexcept {}
        IButtonBase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IButtonBaseFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IButtonBaseFactory>
    {
        IButtonBaseFactory(std::nullptr_t = nullptr) noexcept {}
        IButtonBaseFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IButtonBaseStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IButtonBaseStatics>
    {
        IButtonBaseStatics(std::nullptr_t = nullptr) noexcept {}
        IButtonBaseStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarPanel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarPanel>
    {
        ICalendarPanel(std::nullptr_t = nullptr) noexcept {}
        ICalendarPanel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarViewTemplateSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarViewTemplateSettings>
    {
        ICalendarViewTemplateSettings(std::nullptr_t = nullptr) noexcept {}
        ICalendarViewTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICarouselPanel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICarouselPanel>
    {
        ICarouselPanel(std::nullptr_t = nullptr) noexcept {}
        ICarouselPanel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICarouselPanelFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICarouselPanelFactory>
    {
        ICarouselPanelFactory(std::nullptr_t = nullptr) noexcept {}
        ICarouselPanelFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IColorPickerSlider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorPickerSlider>
    {
        IColorPickerSlider(std::nullptr_t = nullptr) noexcept {}
        IColorPickerSlider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IColorPickerSliderFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorPickerSliderFactory>
    {
        IColorPickerSliderFactory(std::nullptr_t = nullptr) noexcept {}
        IColorPickerSliderFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IColorPickerSliderStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorPickerSliderStatics>
    {
        IColorPickerSliderStatics(std::nullptr_t = nullptr) noexcept {}
        IColorPickerSliderStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IColorSpectrum :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorSpectrum>
    {
        IColorSpectrum(std::nullptr_t = nullptr) noexcept {}
        IColorSpectrum(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IColorSpectrumFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorSpectrumFactory>
    {
        IColorSpectrumFactory(std::nullptr_t = nullptr) noexcept {}
        IColorSpectrumFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IColorSpectrumStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorSpectrumStatics>
    {
        IColorSpectrumStatics(std::nullptr_t = nullptr) noexcept {}
        IColorSpectrumStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBoxTemplateSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBoxTemplateSettings>
    {
        IComboBoxTemplateSettings(std::nullptr_t = nullptr) noexcept {}
        IComboBoxTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBoxTemplateSettings2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBoxTemplateSettings2>
    {
        IComboBoxTemplateSettings2(std::nullptr_t = nullptr) noexcept {}
        IComboBoxTemplateSettings2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICommandBarFlyoutCommandBar :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBarFlyoutCommandBar>
    {
        ICommandBarFlyoutCommandBar(std::nullptr_t = nullptr) noexcept {}
        ICommandBarFlyoutCommandBar(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICommandBarFlyoutCommandBarFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBarFlyoutCommandBarFactory>
    {
        ICommandBarFlyoutCommandBarFactory(std::nullptr_t = nullptr) noexcept {}
        ICommandBarFlyoutCommandBarFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICommandBarFlyoutCommandBarTemplateSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBarFlyoutCommandBarTemplateSettings>
    {
        ICommandBarFlyoutCommandBarTemplateSettings(std::nullptr_t = nullptr) noexcept {}
        ICommandBarFlyoutCommandBarTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICommandBarTemplateSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBarTemplateSettings>
    {
        ICommandBarTemplateSettings(std::nullptr_t = nullptr) noexcept {}
        ICommandBarTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICommandBarTemplateSettings2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBarTemplateSettings2>
    {
        ICommandBarTemplateSettings2(std::nullptr_t = nullptr) noexcept {}
        ICommandBarTemplateSettings2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICommandBarTemplateSettings3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBarTemplateSettings3>
    {
        ICommandBarTemplateSettings3(std::nullptr_t = nullptr) noexcept {}
        ICommandBarTemplateSettings3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICommandBarTemplateSettings4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBarTemplateSettings4>
    {
        ICommandBarTemplateSettings4(std::nullptr_t = nullptr) noexcept {}
        ICommandBarTemplateSettings4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDragCompletedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragCompletedEventArgs>
    {
        IDragCompletedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDragCompletedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDragCompletedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragCompletedEventArgsFactory>
    {
        IDragCompletedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IDragCompletedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDragDeltaEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragDeltaEventArgs>
    {
        IDragDeltaEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDragDeltaEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDragDeltaEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragDeltaEventArgsFactory>
    {
        IDragDeltaEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IDragDeltaEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDragStartedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragStartedEventArgs>
    {
        IDragStartedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDragStartedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDragStartedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragStartedEventArgsFactory>
    {
        IDragStartedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IDragStartedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutBase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutBase>
    {
        IFlyoutBase(std::nullptr_t = nullptr) noexcept {}
        IFlyoutBase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutBase2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutBase2>
    {
        IFlyoutBase2(std::nullptr_t = nullptr) noexcept {}
        IFlyoutBase2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutBase3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutBase3>
    {
        IFlyoutBase3(std::nullptr_t = nullptr) noexcept {}
        IFlyoutBase3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutBase4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutBase4>
    {
        IFlyoutBase4(std::nullptr_t = nullptr) noexcept {}
        IFlyoutBase4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutBase5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutBase5>
    {
        IFlyoutBase5(std::nullptr_t = nullptr) noexcept {}
        IFlyoutBase5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutBase6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutBase6>
    {
        IFlyoutBase6(std::nullptr_t = nullptr) noexcept {}
        IFlyoutBase6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutBaseClosingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutBaseClosingEventArgs>
    {
        IFlyoutBaseClosingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IFlyoutBaseClosingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutBaseFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutBaseFactory>
    {
        IFlyoutBaseFactory(std::nullptr_t = nullptr) noexcept {}
        IFlyoutBaseFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutBaseOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutBaseOverrides>
    {
        IFlyoutBaseOverrides(std::nullptr_t = nullptr) noexcept {}
        IFlyoutBaseOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutBaseOverrides4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutBaseOverrides4>
    {
        IFlyoutBaseOverrides4(std::nullptr_t = nullptr) noexcept {}
        IFlyoutBaseOverrides4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutBaseStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutBaseStatics>
    {
        IFlyoutBaseStatics(std::nullptr_t = nullptr) noexcept {}
        IFlyoutBaseStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutBaseStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutBaseStatics2>
    {
        IFlyoutBaseStatics2(std::nullptr_t = nullptr) noexcept {}
        IFlyoutBaseStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutBaseStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutBaseStatics3>
    {
        IFlyoutBaseStatics3(std::nullptr_t = nullptr) noexcept {}
        IFlyoutBaseStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutBaseStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutBaseStatics5>
    {
        IFlyoutBaseStatics5(std::nullptr_t = nullptr) noexcept {}
        IFlyoutBaseStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutBaseStatics6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutBaseStatics6>
    {
        IFlyoutBaseStatics6(std::nullptr_t = nullptr) noexcept {}
        IFlyoutBaseStatics6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutShowOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutShowOptions>
    {
        IFlyoutShowOptions(std::nullptr_t = nullptr) noexcept {}
        IFlyoutShowOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutShowOptionsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutShowOptionsFactory>
    {
        IFlyoutShowOptionsFactory(std::nullptr_t = nullptr) noexcept {}
        IFlyoutShowOptionsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGeneratorPositionHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGeneratorPositionHelper>
    {
        IGeneratorPositionHelper(std::nullptr_t = nullptr) noexcept {}
        IGeneratorPositionHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGeneratorPositionHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGeneratorPositionHelperStatics>
    {
        IGeneratorPositionHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IGeneratorPositionHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridViewItemPresenter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridViewItemPresenter>
    {
        IGridViewItemPresenter(std::nullptr_t = nullptr) noexcept {}
        IGridViewItemPresenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridViewItemPresenterFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridViewItemPresenterFactory>
    {
        IGridViewItemPresenterFactory(std::nullptr_t = nullptr) noexcept {}
        IGridViewItemPresenterFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridViewItemPresenterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridViewItemPresenterStatics>
    {
        IGridViewItemPresenterStatics(std::nullptr_t = nullptr) noexcept {}
        IGridViewItemPresenterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridViewItemTemplateSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridViewItemTemplateSettings>
    {
        IGridViewItemTemplateSettings(std::nullptr_t = nullptr) noexcept {}
        IGridViewItemTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsChangedEventArgs>
    {
        IItemsChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IItemsChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IJumpListItemBackgroundConverter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IJumpListItemBackgroundConverter>
    {
        IJumpListItemBackgroundConverter(std::nullptr_t = nullptr) noexcept {}
        IJumpListItemBackgroundConverter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IJumpListItemBackgroundConverterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IJumpListItemBackgroundConverterStatics>
    {
        IJumpListItemBackgroundConverterStatics(std::nullptr_t = nullptr) noexcept {}
        IJumpListItemBackgroundConverterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IJumpListItemForegroundConverter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IJumpListItemForegroundConverter>
    {
        IJumpListItemForegroundConverter(std::nullptr_t = nullptr) noexcept {}
        IJumpListItemForegroundConverter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IJumpListItemForegroundConverterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IJumpListItemForegroundConverterStatics>
    {
        IJumpListItemForegroundConverterStatics(std::nullptr_t = nullptr) noexcept {}
        IJumpListItemForegroundConverterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILayoutInformation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILayoutInformation>
    {
        ILayoutInformation(std::nullptr_t = nullptr) noexcept {}
        ILayoutInformation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILayoutInformationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILayoutInformationStatics>
    {
        ILayoutInformationStatics(std::nullptr_t = nullptr) noexcept {}
        ILayoutInformationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILayoutInformationStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILayoutInformationStatics2>
    {
        ILayoutInformationStatics2(std::nullptr_t = nullptr) noexcept {}
        ILayoutInformationStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewItemPresenter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewItemPresenter>
    {
        IListViewItemPresenter(std::nullptr_t = nullptr) noexcept {}
        IListViewItemPresenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewItemPresenter2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewItemPresenter2>
    {
        IListViewItemPresenter2(std::nullptr_t = nullptr) noexcept {}
        IListViewItemPresenter2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewItemPresenter3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewItemPresenter3>
    {
        IListViewItemPresenter3(std::nullptr_t = nullptr) noexcept {}
        IListViewItemPresenter3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewItemPresenter4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewItemPresenter4>
    {
        IListViewItemPresenter4(std::nullptr_t = nullptr) noexcept {}
        IListViewItemPresenter4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewItemPresenterFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewItemPresenterFactory>
    {
        IListViewItemPresenterFactory(std::nullptr_t = nullptr) noexcept {}
        IListViewItemPresenterFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewItemPresenterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewItemPresenterStatics>
    {
        IListViewItemPresenterStatics(std::nullptr_t = nullptr) noexcept {}
        IListViewItemPresenterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewItemPresenterStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewItemPresenterStatics2>
    {
        IListViewItemPresenterStatics2(std::nullptr_t = nullptr) noexcept {}
        IListViewItemPresenterStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewItemPresenterStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewItemPresenterStatics3>
    {
        IListViewItemPresenterStatics3(std::nullptr_t = nullptr) noexcept {}
        IListViewItemPresenterStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewItemPresenterStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewItemPresenterStatics4>
    {
        IListViewItemPresenterStatics4(std::nullptr_t = nullptr) noexcept {}
        IListViewItemPresenterStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewItemTemplateSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewItemTemplateSettings>
    {
        IListViewItemTemplateSettings(std::nullptr_t = nullptr) noexcept {}
        IListViewItemTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILoopingSelector :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILoopingSelector>
    {
        ILoopingSelector(std::nullptr_t = nullptr) noexcept {}
        ILoopingSelector(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILoopingSelectorItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILoopingSelectorItem>
    {
        ILoopingSelectorItem(std::nullptr_t = nullptr) noexcept {}
        ILoopingSelectorItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILoopingSelectorPanel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILoopingSelectorPanel>
    {
        ILoopingSelectorPanel(std::nullptr_t = nullptr) noexcept {}
        ILoopingSelectorPanel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILoopingSelectorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILoopingSelectorStatics>
    {
        ILoopingSelectorStatics(std::nullptr_t = nullptr) noexcept {}
        ILoopingSelectorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutItemTemplateSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutItemTemplateSettings>
    {
        IMenuFlyoutItemTemplateSettings(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutItemTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutPresenterTemplateSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutPresenterTemplateSettings>
    {
        IMenuFlyoutPresenterTemplateSettings(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutPresenterTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewItemPresenter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemPresenter>
    {
        INavigationViewItemPresenter(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemPresenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewItemPresenterFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemPresenterFactory>
    {
        INavigationViewItemPresenterFactory(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemPresenterFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewItemPresenterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemPresenterStatics>
    {
        INavigationViewItemPresenterStatics(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemPresenterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IOrientedVirtualizingPanel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientedVirtualizingPanel>
    {
        IOrientedVirtualizingPanel(std::nullptr_t = nullptr) noexcept {}
        IOrientedVirtualizingPanel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IOrientedVirtualizingPanelFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOrientedVirtualizingPanelFactory>
    {
        IOrientedVirtualizingPanelFactory(std::nullptr_t = nullptr) noexcept {}
        IOrientedVirtualizingPanelFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPickerFlyoutBase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPickerFlyoutBase>
    {
        IPickerFlyoutBase(std::nullptr_t = nullptr) noexcept {}
        IPickerFlyoutBase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPickerFlyoutBaseFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPickerFlyoutBaseFactory>
    {
        IPickerFlyoutBaseFactory(std::nullptr_t = nullptr) noexcept {}
        IPickerFlyoutBaseFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPickerFlyoutBaseOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPickerFlyoutBaseOverrides>
    {
        IPickerFlyoutBaseOverrides(std::nullptr_t = nullptr) noexcept {}
        IPickerFlyoutBaseOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPickerFlyoutBaseStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPickerFlyoutBaseStatics>
    {
        IPickerFlyoutBaseStatics(std::nullptr_t = nullptr) noexcept {}
        IPickerFlyoutBaseStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPivotHeaderItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPivotHeaderItem>
    {
        IPivotHeaderItem(std::nullptr_t = nullptr) noexcept {}
        IPivotHeaderItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPivotHeaderItemFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPivotHeaderItemFactory>
    {
        IPivotHeaderItemFactory(std::nullptr_t = nullptr) noexcept {}
        IPivotHeaderItemFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPivotHeaderPanel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPivotHeaderPanel>
    {
        IPivotHeaderPanel(std::nullptr_t = nullptr) noexcept {}
        IPivotHeaderPanel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPivotPanel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPivotPanel>
    {
        IPivotPanel(std::nullptr_t = nullptr) noexcept {}
        IPivotPanel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPopup :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPopup>
    {
        IPopup(std::nullptr_t = nullptr) noexcept {}
        IPopup(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPopup2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPopup2>
    {
        IPopup2(std::nullptr_t = nullptr) noexcept {}
        IPopup2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPopup3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPopup3>
    {
        IPopup3(std::nullptr_t = nullptr) noexcept {}
        IPopup3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPopup4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPopup4>
    {
        IPopup4(std::nullptr_t = nullptr) noexcept {}
        IPopup4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPopupStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPopupStatics>
    {
        IPopupStatics(std::nullptr_t = nullptr) noexcept {}
        IPopupStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPopupStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPopupStatics2>
    {
        IPopupStatics2(std::nullptr_t = nullptr) noexcept {}
        IPopupStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPopupStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPopupStatics3>
    {
        IPopupStatics3(std::nullptr_t = nullptr) noexcept {}
        IPopupStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPopupStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPopupStatics4>
    {
        IPopupStatics4(std::nullptr_t = nullptr) noexcept {}
        IPopupStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProgressBarTemplateSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProgressBarTemplateSettings>
    {
        IProgressBarTemplateSettings(std::nullptr_t = nullptr) noexcept {}
        IProgressBarTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProgressRingTemplateSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProgressRingTemplateSettings>
    {
        IProgressRingTemplateSettings(std::nullptr_t = nullptr) noexcept {}
        IProgressRingTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRangeBase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRangeBase>
    {
        IRangeBase(std::nullptr_t = nullptr) noexcept {}
        IRangeBase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRangeBaseFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRangeBaseFactory>
    {
        IRangeBaseFactory(std::nullptr_t = nullptr) noexcept {}
        IRangeBaseFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRangeBaseOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRangeBaseOverrides>
    {
        IRangeBaseOverrides(std::nullptr_t = nullptr) noexcept {}
        IRangeBaseOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRangeBaseStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRangeBaseStatics>
    {
        IRangeBaseStatics(std::nullptr_t = nullptr) noexcept {}
        IRangeBaseStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRangeBaseValueChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRangeBaseValueChangedEventArgs>
    {
        IRangeBaseValueChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRangeBaseValueChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRepeatButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRepeatButton>
    {
        IRepeatButton(std::nullptr_t = nullptr) noexcept {}
        IRepeatButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRepeatButtonStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRepeatButtonStatics>
    {
        IRepeatButtonStatics(std::nullptr_t = nullptr) noexcept {}
        IRepeatButtonStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollBar :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollBar>
    {
        IScrollBar(std::nullptr_t = nullptr) noexcept {}
        IScrollBar(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollBarStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollBarStatics>
    {
        IScrollBarStatics(std::nullptr_t = nullptr) noexcept {}
        IScrollBarStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollEventArgs>
    {
        IScrollEventArgs(std::nullptr_t = nullptr) noexcept {}
        IScrollEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollSnapPointsInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollSnapPointsInfo>
    {
        IScrollSnapPointsInfo(std::nullptr_t = nullptr) noexcept {}
        IScrollSnapPointsInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISelector :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelector>
    {
        ISelector(std::nullptr_t = nullptr) noexcept {}
        ISelector(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISelectorFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectorFactory>
    {
        ISelectorFactory(std::nullptr_t = nullptr) noexcept {}
        ISelectorFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISelectorItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectorItem>
    {
        ISelectorItem(std::nullptr_t = nullptr) noexcept {}
        ISelectorItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISelectorItemFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectorItemFactory>
    {
        ISelectorItemFactory(std::nullptr_t = nullptr) noexcept {}
        ISelectorItemFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISelectorItemStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectorItemStatics>
    {
        ISelectorItemStatics(std::nullptr_t = nullptr) noexcept {}
        ISelectorItemStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISelectorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectorStatics>
    {
        ISelectorStatics(std::nullptr_t = nullptr) noexcept {}
        ISelectorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISettingsFlyoutTemplateSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISettingsFlyoutTemplateSettings>
    {
        ISettingsFlyoutTemplateSettings(std::nullptr_t = nullptr) noexcept {}
        ISettingsFlyoutTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISplitViewTemplateSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplitViewTemplateSettings>
    {
        ISplitViewTemplateSettings(std::nullptr_t = nullptr) noexcept {}
        ISplitViewTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IThumb :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IThumb>
    {
        IThumb(std::nullptr_t = nullptr) noexcept {}
        IThumb(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IThumbStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IThumbStatics>
    {
        IThumbStatics(std::nullptr_t = nullptr) noexcept {}
        IThumbStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITickBar :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITickBar>
    {
        ITickBar(std::nullptr_t = nullptr) noexcept {}
        ITickBar(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITickBarStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITickBarStatics>
    {
        ITickBarStatics(std::nullptr_t = nullptr) noexcept {}
        ITickBarStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleButton :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleButton>
    {
        IToggleButton(std::nullptr_t = nullptr) noexcept {}
        IToggleButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleButtonFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleButtonFactory>
    {
        IToggleButtonFactory(std::nullptr_t = nullptr) noexcept {}
        IToggleButtonFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleButtonOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleButtonOverrides>
    {
        IToggleButtonOverrides(std::nullptr_t = nullptr) noexcept {}
        IToggleButtonOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleButtonStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleButtonStatics>
    {
        IToggleButtonStatics(std::nullptr_t = nullptr) noexcept {}
        IToggleButtonStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleSwitchTemplateSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleSwitchTemplateSettings>
    {
        IToggleSwitchTemplateSettings(std::nullptr_t = nullptr) noexcept {}
        IToggleSwitchTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToolTipTemplateSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToolTipTemplateSettings>
    {
        IToolTipTemplateSettings(std::nullptr_t = nullptr) noexcept {}
        IToolTipTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
