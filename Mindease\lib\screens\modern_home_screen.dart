import 'package:flutter/material.dart';
import 'package:myapp/theme/app_theme.dart';
import 'package:myapp/components/modern_card_widgets.dart';
import 'package:myapp/components/modern_navigation.dart';

import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class ModernHomeScreen extends StatefulWidget {
  final String userId;

  const ModernHomeScreen({
    super.key,
    required this.userId,
  });

  @override
  State<ModernHomeScreen> createState() => _ModernHomeScreenState();
}

class _ModernHomeScreenState extends State<ModernHomeScreen>
    with TickerProviderStateMixin {
  Map<String, dynamic>? userDetails;
  Map<String, dynamic> dashboardStats = {};
  bool isLoading = true;
  int currentNavIndex = 0;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _fetchUserData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _fetchUserData() async {
    setState(() {
      isLoading = true;
    });

    try {
      // Fetch user details
      final userResponse = await http.get(
        Uri.parse('http://localhost:3000/api/users/${widget.userId}'),
        headers: {'Content-Type': 'application/json'},
      );

      if (userResponse.statusCode == 200) {
        final userData = jsonDecode(userResponse.body);
        setState(() {
          userDetails = userData['user'] ?? userData;
        });
      }

      // Fetch dashboard stats
      await _fetchDashboardStats();

      setState(() {
        isLoading = false;
      });

      _animationController.forward();
    } catch (e) {
      print('Error fetching user data: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _fetchDashboardStats() async {
    try {
      // Fetch mood entries count
      final moodResponse = await http.get(
        Uri.parse(
            'http://localhost:3000/api/mood-entries/user/${widget.userId}'),
        headers: {'Content-Type': 'application/json'},
      );

      // Fetch appointments count
      final appointmentsResponse = await http.get(
        Uri.parse(
            'http://localhost:3000/api/appointments/user/${widget.userId}'),
        headers: {'Content-Type': 'application/json'},
      );

      int moodEntries = 0;
      int appointments = 0;
      int journalEntries = 0;

      if (moodResponse.statusCode == 200) {
        final moodData = jsonDecode(moodResponse.body);
        moodEntries = (moodData['moodEntries'] as List?)?.length ?? 0;
      }

      if (appointmentsResponse.statusCode == 200) {
        final appointmentData = jsonDecode(appointmentsResponse.body);
        appointments = (appointmentData['appointments'] as List?)?.length ?? 0;
      }

      setState(() {
        dashboardStats = {
          'moodEntries': moodEntries,
          'appointments': appointments,
          'journalEntries': journalEntries,
          'weeklyProgress': 15.5, // Mock data
        };
      });
    } catch (e) {
      debugPrint('Error fetching dashboard stats: $e');
    }
  }

  void _onNavItemTapped(int index) {
    setState(() {
      currentNavIndex = index;
    });

    switch (index) {
      case 0:
        // Already on home
        break;
      case 1:
        Navigator.pushNamed(context, '/mood_journal');
        break;
      case 2:
        Navigator.pushNamed(
          context,
          '/analysis',
          arguments: {'userId': widget.userId},
        );
        break;
      case 3:
        Navigator.pushNamed(context, '/settings');
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Scaffold(
        backgroundColor: AppTheme.backgroundColor,
        body: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.backgroundGradient,
          ),
          child: const Center(
            child: CircularProgressIndicator(
              color: AppTheme.primaryColor,
            ),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Container(
        decoration: BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildHeader(),
                  _buildQuickStats(),
                  _buildQuickActions(),
                  _buildRecentActivity(),
                  const SizedBox(height: AppTheme.spacingXXL),
                ],
              ),
            ),
          ),
        ),
      ),
      bottomNavigationBar: ModernBottomNavBar(
        currentIndex: currentNavIndex,
        onTap: _onNavItemTapped,
        items: const [
          ModernNavItem(
            icon: Icons.home_outlined,
            activeIcon: Icons.home,
            label: 'Home',
          ),
          ModernNavItem(
            icon: Icons.book_outlined,
            activeIcon: Icons.book,
            label: 'Journal',
          ),
          ModernNavItem(
            icon: Icons.analytics_outlined,
            activeIcon: Icons.analytics,
            label: 'Analysis',
          ),
          ModernNavItem(
            icon: Icons.settings_outlined,
            activeIcon: Icons.settings,
            label: 'Settings',
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingL),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppTheme.radiusXL),
          bottomRight: Radius.circular(AppTheme.radiusXL),
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingS),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(AppTheme.radiusM),
                ),
                child: const Icon(
                  Icons.waving_hand,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: AppTheme.spacingM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Hello, ${userDetails?['username'] ?? 'User'}!',
                      style: AppTheme.headingLarge.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'How are you feeling today?',
                      style: AppTheme.bodyLarge.copyWith(
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    '/appointments',
                    arguments: {'userId': widget.userId},
                  );
                },
                icon: const Icon(
                  Icons.calendar_today,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingL),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pushNamed(context, '/mood_entry');
            },
            icon: const Icon(Icons.sentiment_satisfied),
            label: const Text('Track Your Mood'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacingL,
                vertical: AppTheme.spacingM,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppTheme.radiusL),
              ),
              minimumSize: const Size(double.infinity, 50),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Your Progress',
            style: AppTheme.headingMedium.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingL),
          Row(
            children: [
              Expanded(
                child: ModernStatsCard(
                  title: 'Mood Entries',
                  value: '${dashboardStats['moodEntries'] ?? 0}',
                  icon: Icons.sentiment_satisfied,
                  color: AppTheme.primaryColor,
                  subtitle: 'This month',
                  onTap: () => Navigator.pushNamed(context, '/mood_journal'),
                ),
              ),
              const SizedBox(width: AppTheme.spacingM),
              Expanded(
                child: ModernStatsCard(
                  title: 'Sessions',
                  value: '${dashboardStats['appointments'] ?? 0}',
                  icon: Icons.psychology,
                  color: AppTheme.secondaryColor,
                  subtitle: 'Completed',
                  onTap: () => Navigator.pushNamed(
                    context,
                    '/appointments',
                    arguments: {'userId': widget.userId},
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Actions',
            style: AppTheme.headingMedium.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingL),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: AppTheme.spacingM,
            mainAxisSpacing: AppTheme.spacingM,
            childAspectRatio: 1.2,
            children: [
              ModernQuickActionCard(
                title: 'Find Therapist',
                icon: Icons.psychology,
                color: AppTheme.primaryColor,
                onTap: () => Navigator.pushNamed(
                  context,
                  '/therapist_list',
                  arguments: {'userId': widget.userId},
                ),
              ),
              ModernQuickActionCard(
                title: 'Depression Test',
                icon: Icons.quiz,
                color: AppTheme.accentColor,
                onTap: () => Navigator.pushNamed(
                  context,
                  '/depression_test',
                  arguments: {'userId': widget.userId},
                ),
              ),
              ModernQuickActionCard(
                title: 'View Analysis',
                icon: Icons.analytics,
                color: AppTheme.secondaryColor,
                onTap: () => Navigator.pushNamed(
                  context,
                  '/analysis',
                  arguments: {'userId': widget.userId},
                ),
              ),
              ModernQuickActionCard(
                title: 'Journal Entry',
                icon: Icons.edit_note,
                color: AppTheme.successColor,
                onTap: () => Navigator.pushNamed(context, '/journaling'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Activity',
                style: AppTheme.headingMedium.copyWith(
                  color: AppTheme.textPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () => Navigator.pushNamed(
                  context,
                  '/analysis',
                  arguments: {'userId': widget.userId},
                ),
                child: Text(
                  'View All',
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingL),
          ModernProgressCard(
            title: 'Weekly Mood Tracking',
            subtitle: 'Keep up the great work!',
            progress: (dashboardStats['weeklyProgress'] ?? 0) / 100,
            color: AppTheme.primaryColor,
            icon: Icons.trending_up,
            onTap: () => Navigator.pushNamed(context, '/mood_journal'),
          ),
          const SizedBox(height: AppTheme.spacingM),
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingL),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(AppTheme.radiusL),
              boxShadow: AppTheme.softShadow,
            ),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: AppTheme.successColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppTheme.radiusM),
                  ),
                  child: const Icon(
                    Icons.tips_and_updates,
                    color: AppTheme.successColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Daily Tip',
                        style: AppTheme.headingMedium.copyWith(
                          color: AppTheme.textPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Take 5 minutes today to practice deep breathing exercises.',
                        style: AppTheme.bodyMedium.copyWith(
                          color: AppTheme.textSecondary,
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
