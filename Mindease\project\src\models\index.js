
// Firebase-compatible models for MindEase App
// These provide a compatibility layer for existing code while using Firebase

const firebaseService = require('../services/firebaseService');

// Firebase-compatible User model
class User {
  static async find(query = {}) {
    await firebaseService.initialize();
    const usersQuery = await firebaseService.db.collection('users').get();
    return usersQuery.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  static async findById(id) {
    return await firebaseService.getUserById(id);
  }

  static async findOne(query) {
    await firebaseService.initialize();

    if (query.email) {
      return await firebaseService.getUserByEmail(query.email);
    }

    // Handle other query types
    const usersQuery = await firebaseService.db.collection('users').get();
    const users = usersQuery.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    for (const [key, value] of Object.entries(query)) {
      const user = users.find(u => u[key] === value);
      if (user) return user;
    }

    return null;
  }

  static async findByIdAndUpdate(id, updateData, options = {}) {
    const updatedUser = await firebaseService.updateUser(id, updateData);
    return updatedUser;
  }

  static async create(userData) {
    return await firebaseService.createUser(userData);
  }

  // Add select method for compatibility
  static select(fields) {
    return this; // Return this for chaining
  }

  // Add populate method for compatibility
  static populate(field) {
    return this; // Return this for chaining
  }
}



// Firebase-compatible Therapist model
class Therapist {
  static async find(query = {}) {
    await firebaseService.initialize();
    return await firebaseService.getAllTherapists(query);
  }

  static async findById(id) {
    return await firebaseService.getTherapistById(id);
  }

  static async findOne(query) {
    await firebaseService.initialize();

    if (query.email) {
      return await firebaseService.getTherapistByEmail(query.email);
    }

    const therapists = await firebaseService.getAllTherapists();
    for (const [key, value] of Object.entries(query)) {
      const therapist = therapists.find(t => t[key] === value);
      if (therapist) return therapist;
    }

    return null;
  }

  static async findByIdAndUpdate(id, updateData, options = {}) {
    return await firebaseService.updateTherapist(id, updateData);
  }

  static async create(therapistData) {
    return await firebaseService.createTherapist(therapistData);
  }

  static select(fields) {
    return this;
  }

  static populate(field) {
    return this;
  }
}

// Firebase-compatible Appointment model
class Appointment {
  static async find(query = {}) {
    await firebaseService.initialize();
    const appointmentsQuery = await firebaseService.db.collection('appointments').get();
    let appointments = appointmentsQuery.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    // Apply query filters
    if (query.userId) {
      appointments = appointments.filter(a => a.userId === query.userId);
    }
    if (query.therapistId) {
      appointments = appointments.filter(a => a.therapistId === query.therapistId);
    }

    return appointments;
  }

  static async findById(id) {
    return await firebaseService.getAppointmentById(id);
  }

  static async create(appointmentData) {
    return await firebaseService.createAppointment(appointmentData);
  }

  static async findByIdAndUpdate(id, updateData, options = {}) {
    return await firebaseService.updateAppointment(id, updateData);
  }

  static select(fields) {
    return this;
  }

  static populate(field) {
    return this;
  }
}

// Firebase-compatible Mood model
class Mood {
  static async find(query = {}) {
    await firebaseService.initialize();
    if (query.userId) {
      return await firebaseService.getMoodEntriesByUserId(query.userId);
    }

    const moodQuery = await firebaseService.db.collection('moodEntries').get();
    return moodQuery.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  static async create(moodData) {
    return await firebaseService.createMoodEntry(moodData);
  }

  static select(fields) {
    return this;
  }
}

// Firebase-compatible JournalEntry model
class JournalEntry {
  static async find(query = {}) {
    await firebaseService.initialize();
    if (query.userId) {
      return await firebaseService.getJournalEntriesByUserId(query.userId);
    }

    const journalQuery = await firebaseService.db.collection('journalEntries').get();
    return journalQuery.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  static async create(journalData) {
    return await firebaseService.createJournalEntry(journalData);
  }

  static select(fields) {
    return this;
  }
}

// Firebase-compatible TestResult model
class TestResult {
  static async find(query = {}) {
    await firebaseService.initialize();
    if (query.userId) {
      return await firebaseService.getTestResultsByUserId(query.userId);
    }

    const testQuery = await firebaseService.db.collection('testResults').get();
    return testQuery.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  static async create(testData) {
    return await firebaseService.createTestResult(testData);
  }

  static select(fields) {
    return this;
  }
}

// Firebase-compatible Payment model (placeholder)
class Payment {
  static async find(query = {}) {
    await firebaseService.initialize();
    const paymentQuery = await firebaseService.db.collection('payments').get();
    return paymentQuery.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  static async create(paymentData) {
    await firebaseService.initialize();
    const paymentRef = firebaseService.db.collection('payments').doc();
    await paymentRef.set(paymentData);
    return { id: paymentRef.id, ...paymentData };
  }

  static select(fields) {
    return this;
  }
}

// Export Firebase-compatible models
module.exports = {
  User,
  JournalEntry,
  Payment,
  Therapist,
  Mood,
  Appointment,
  TestResult,
};
