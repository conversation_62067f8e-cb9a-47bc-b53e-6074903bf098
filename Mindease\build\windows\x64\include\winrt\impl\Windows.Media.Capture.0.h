// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Media_Capture_0_H
#define WINRT_Windows_Media_Capture_0_H
WINRT_EXPORT namespace winrt::Windows::Devices::Enumeration
{
    enum class Panel : int32_t;
}
WINRT_EXPORT namespace winrt::Windows::Foundation
{
    struct EventRegistrationToken;
    struct IAsyncAction;
    template <typename TResult> struct __declspec(empty_bases) IAsyncOperation;
    template <typename T> struct __declspec(empty_bases) IReference;
    struct Rect;
    struct Size;
    template <typename TSender, typename TResult> struct __declspec(empty_bases) TypedEventHandler;
    struct Uri;
}
WINRT_EXPORT namespace winrt::Windows::Foundation::Collections
{
    template <typename T> struct __declspec(empty_bases) IIterable;
    template <typename K, typename V> struct __declspec(empty_bases) IMapView;
    struct IPropertySet;
    template <typename T> struct __declspec(empty_bases) IVectorView;
}
WINRT_EXPORT namespace winrt::Windows::Graphics::DirectX::Direct3D11
{
    struct IDirect3DDevice;
}
WINRT_EXPORT namespace winrt::Windows::Graphics::Imaging
{
    struct BitmapPropertySet;
    struct BitmapSize;
    struct SoftwareBitmap;
}
WINRT_EXPORT namespace winrt::Windows::Media
{
    enum class AudioProcessing : int32_t;
    struct IMediaExtension;
    struct VideoFrame;
}
WINRT_EXPORT namespace winrt::Windows::Media::Capture::Core
{
    struct VariablePhotoSequenceCapture;
}
WINRT_EXPORT namespace winrt::Windows::Media::Capture::Frames
{
    struct MediaFrameReader;
    struct MediaFrameSource;
    struct MediaFrameSourceGroup;
    struct MediaFrameSourceInfo;
    struct MultiSourceMediaFrameReader;
}
WINRT_EXPORT namespace winrt::Windows::Media::Core
{
    struct IMediaSource;
}
WINRT_EXPORT namespace winrt::Windows::Media::Devices
{
    enum class AdvancedPhotoMode : int32_t;
    struct AudioDeviceController;
    enum class CameraStreamState : int32_t;
    enum class CaptureSceneMode : int32_t;
    enum class MediaCaptureFocusState : int32_t;
    enum class MediaCapturePauseBehavior : int32_t;
    struct VideoDeviceController;
}
WINRT_EXPORT namespace winrt::Windows::Media::Effects
{
    struct IAudioEffectDefinition;
    struct IVideoEffectDefinition;
}
WINRT_EXPORT namespace winrt::Windows::Media::MediaProperties
{
    struct IMediaEncodingProperties;
    struct ImageEncodingProperties;
    struct MediaEncodingProfile;
    struct MediaPropertySet;
    struct MediaRatio;
    struct VideoEncodingProperties;
}
WINRT_EXPORT namespace winrt::Windows::Security::Credentials
{
    struct PasswordCredential;
}
WINRT_EXPORT namespace winrt::Windows::Storage
{
    struct IStorageFile;
    struct StorageFile;
}
WINRT_EXPORT namespace winrt::Windows::Storage::Streams
{
    struct IRandomAccessStream;
}
WINRT_EXPORT namespace winrt::Windows::UI::WindowManagement
{
    struct DisplayRegion;
}
WINRT_EXPORT namespace winrt::Windows::Media::Capture
{
    enum class CameraCaptureUIMaxPhotoResolution : int32_t
    {
        HighestAvailable = 0,
        VerySmallQvga = 1,
        SmallVga = 2,
        MediumXga = 3,
        Large3M = 4,
        VeryLarge5M = 5,
    };
    enum class CameraCaptureUIMaxVideoResolution : int32_t
    {
        HighestAvailable = 0,
        LowDefinition = 1,
        StandardDefinition = 2,
        HighDefinition = 3,
    };
    enum class CameraCaptureUIMode : int32_t
    {
        PhotoOrVideo = 0,
        Photo = 1,
        Video = 2,
    };
    enum class CameraCaptureUIPhotoFormat : int32_t
    {
        Jpeg = 0,
        Png = 1,
        JpegXR = 2,
    };
    enum class CameraCaptureUIVideoFormat : int32_t
    {
        Mp4 = 0,
        Wmv = 1,
    };
    enum class KnownVideoProfile : int32_t
    {
        VideoRecording = 0,
        HighQualityPhoto = 1,
        BalancedVideoAndPhoto = 2,
        VideoConferencing = 3,
        PhotoSequence = 4,
        HighFrameRate = 5,
        VariablePhotoSequence = 6,
        HdrWithWcgVideo = 7,
        HdrWithWcgPhoto = 8,
        VideoHdr8 = 9,
        CompressedCamera = 10,
    };
    enum class MediaCaptureDeviceExclusiveControlReleaseMode : int32_t
    {
        OnDispose = 0,
        OnAllStreamsStopped = 1,
    };
    enum class MediaCaptureDeviceExclusiveControlStatus : int32_t
    {
        ExclusiveControlAvailable = 0,
        SharedReadOnlyAvailable = 1,
    };
    enum class MediaCaptureMemoryPreference : int32_t
    {
        Auto = 0,
        Cpu = 1,
    };
    enum class MediaCaptureSharingMode : int32_t
    {
        ExclusiveControl = 0,
        SharedReadOnly = 1,
    };
    enum class MediaCaptureThermalStatus : int32_t
    {
        Normal = 0,
        Overheated = 1,
    };
    enum class MediaCategory : int32_t
    {
        Other = 0,
        Communications = 1,
        Media = 2,
        GameChat = 3,
        Speech = 4,
        FarFieldSpeech = 5,
        UniformSpeech = 6,
        VoiceTyping = 7,
    };
    enum class MediaStreamType : int32_t
    {
        VideoPreview = 0,
        VideoRecord = 1,
        Audio = 2,
        Photo = 3,
        Metadata = 4,
    };
    enum class PhotoCaptureSource : int32_t
    {
        Auto = 0,
        VideoPreview = 1,
        Photo = 2,
    };
    enum class PowerlineFrequency : int32_t
    {
        Disabled = 0,
        FiftyHertz = 1,
        SixtyHertz = 2,
        Auto = 3,
    };
    enum class StreamingCaptureMode : int32_t
    {
        AudioAndVideo = 0,
        Audio = 1,
        Video = 2,
    };
    enum class VideoDeviceCharacteristic : int32_t
    {
        AllStreamsIndependent = 0,
        PreviewRecordStreamsIdentical = 1,
        PreviewPhotoStreamsIdentical = 2,
        RecordPhotoStreamsIdentical = 3,
        AllStreamsIdentical = 4,
    };
    enum class VideoRotation : int32_t
    {
        None = 0,
        Clockwise90Degrees = 1,
        Clockwise180Degrees = 2,
        Clockwise270Degrees = 3,
    };
    struct IAdvancedCapturedPhoto;
    struct IAdvancedCapturedPhoto2;
    struct IAdvancedPhotoCapture;
    struct IAppCapture;
    struct IAppCaptureStatics;
    struct IAppCaptureStatics2;
    struct ICameraCaptureUI;
    struct ICameraCaptureUIPhotoCaptureSettings;
    struct ICameraCaptureUIVideoCaptureSettings;
    struct ICapturedFrame;
    struct ICapturedFrame2;
    struct ICapturedFrameControlValues;
    struct ICapturedFrameControlValues2;
    struct ICapturedFrameWithSoftwareBitmap;
    struct ICapturedPhoto;
    struct ILowLagMediaRecording;
    struct ILowLagMediaRecording2;
    struct ILowLagMediaRecording3;
    struct ILowLagPhotoCapture;
    struct ILowLagPhotoSequenceCapture;
    struct IMediaCapture;
    struct IMediaCapture2;
    struct IMediaCapture3;
    struct IMediaCapture4;
    struct IMediaCapture5;
    struct IMediaCapture6;
    struct IMediaCapture7;
    struct IMediaCaptureDeviceExclusiveControlStatusChangedEventArgs;
    struct IMediaCaptureFailedEventArgs;
    struct IMediaCaptureFocusChangedEventArgs;
    struct IMediaCaptureInitializationSettings;
    struct IMediaCaptureInitializationSettings2;
    struct IMediaCaptureInitializationSettings3;
    struct IMediaCaptureInitializationSettings4;
    struct IMediaCaptureInitializationSettings5;
    struct IMediaCaptureInitializationSettings6;
    struct IMediaCaptureInitializationSettings7;
    struct IMediaCapturePauseResult;
    struct IMediaCaptureRelativePanelWatcher;
    struct IMediaCaptureSettings;
    struct IMediaCaptureSettings2;
    struct IMediaCaptureSettings3;
    struct IMediaCaptureStatics;
    struct IMediaCaptureStopResult;
    struct IMediaCaptureVideoPreview;
    struct IMediaCaptureVideoProfile;
    struct IMediaCaptureVideoProfile2;
    struct IMediaCaptureVideoProfileMediaDescription;
    struct IMediaCaptureVideoProfileMediaDescription2;
    struct IOptionalReferencePhotoCapturedEventArgs;
    struct IPhotoCapturedEventArgs;
    struct IPhotoConfirmationCapturedEventArgs;
    struct IVideoStreamConfiguration;
    struct AdvancedCapturedPhoto;
    struct AdvancedPhotoCapture;
    struct AppCapture;
    struct CameraCaptureUI;
    struct CameraCaptureUIPhotoCaptureSettings;
    struct CameraCaptureUIVideoCaptureSettings;
    struct CapturedFrame;
    struct CapturedFrameControlValues;
    struct CapturedPhoto;
    struct LowLagMediaRecording;
    struct LowLagPhotoCapture;
    struct LowLagPhotoSequenceCapture;
    struct MediaCapture;
    struct MediaCaptureDeviceExclusiveControlStatusChangedEventArgs;
    struct MediaCaptureFailedEventArgs;
    struct MediaCaptureFocusChangedEventArgs;
    struct MediaCaptureInitializationSettings;
    struct MediaCapturePauseResult;
    struct MediaCaptureRelativePanelWatcher;
    struct MediaCaptureSettings;
    struct MediaCaptureStopResult;
    struct MediaCaptureVideoProfile;
    struct MediaCaptureVideoProfileMediaDescription;
    struct OptionalReferencePhotoCapturedEventArgs;
    struct PhotoCapturedEventArgs;
    struct PhotoConfirmationCapturedEventArgs;
    struct VideoStreamConfiguration;
    struct WhiteBalanceGain;
    struct MediaCaptureFailedEventHandler;
    struct RecordLimitationExceededEventHandler;
}
namespace winrt::impl
{
    template <> struct category<winrt::Windows::Media::Capture::IAdvancedCapturedPhoto>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IAdvancedCapturedPhoto2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IAdvancedPhotoCapture>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IAppCapture>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IAppCaptureStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IAppCaptureStatics2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::ICameraCaptureUI>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::ICameraCaptureUIPhotoCaptureSettings>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::ICameraCaptureUIVideoCaptureSettings>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::ICapturedFrame>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::ICapturedFrame2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::ICapturedFrameControlValues>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::ICapturedFrameControlValues2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::ICapturedFrameWithSoftwareBitmap>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::ICapturedPhoto>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::ILowLagMediaRecording>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::ILowLagMediaRecording2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::ILowLagMediaRecording3>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::ILowLagPhotoCapture>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::ILowLagPhotoSequenceCapture>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCapture>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCapture2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCapture3>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCapture4>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCapture5>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCapture6>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCapture7>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCaptureDeviceExclusiveControlStatusChangedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCaptureFailedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCaptureFocusChangedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings3>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings4>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings5>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings6>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings7>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCapturePauseResult>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCaptureRelativePanelWatcher>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCaptureSettings>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCaptureSettings2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCaptureSettings3>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCaptureStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCaptureStopResult>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCaptureVideoPreview>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCaptureVideoProfile>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCaptureVideoProfile2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IOptionalReferencePhotoCapturedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IPhotoCapturedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IPhotoConfirmationCapturedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::IVideoStreamConfiguration>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Media::Capture::AdvancedCapturedPhoto>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::AdvancedPhotoCapture>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::AppCapture>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::CameraCaptureUI>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::CameraCaptureUIPhotoCaptureSettings>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::CameraCaptureUIVideoCaptureSettings>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::CapturedFrame>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::CapturedFrameControlValues>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::CapturedPhoto>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::LowLagMediaRecording>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::LowLagPhotoCapture>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::LowLagPhotoSequenceCapture>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::MediaCapture>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::MediaCaptureDeviceExclusiveControlStatusChangedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::MediaCaptureFailedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::MediaCaptureFocusChangedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::MediaCaptureInitializationSettings>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::MediaCapturePauseResult>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::MediaCaptureRelativePanelWatcher>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::MediaCaptureSettings>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::MediaCaptureStopResult>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::MediaCaptureVideoProfile>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::OptionalReferencePhotoCapturedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::PhotoCapturedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::PhotoConfirmationCapturedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::VideoStreamConfiguration>{ using type = class_category; };
    template <> struct category<winrt::Windows::Media::Capture::CameraCaptureUIMaxPhotoResolution>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Media::Capture::CameraCaptureUIMaxVideoResolution>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Media::Capture::CameraCaptureUIMode>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Media::Capture::CameraCaptureUIPhotoFormat>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Media::Capture::CameraCaptureUIVideoFormat>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Media::Capture::KnownVideoProfile>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Media::Capture::MediaCaptureDeviceExclusiveControlReleaseMode>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Media::Capture::MediaCaptureDeviceExclusiveControlStatus>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Media::Capture::MediaCaptureMemoryPreference>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Media::Capture::MediaCaptureSharingMode>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Media::Capture::MediaCaptureThermalStatus>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Media::Capture::MediaCategory>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Media::Capture::MediaStreamType>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Media::Capture::PhotoCaptureSource>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Media::Capture::PowerlineFrequency>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Media::Capture::StreamingCaptureMode>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Media::Capture::VideoDeviceCharacteristic>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Media::Capture::VideoRotation>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Media::Capture::WhiteBalanceGain>{ using type = struct_category<double, double, double>; };
    template <> struct category<winrt::Windows::Media::Capture::MediaCaptureFailedEventHandler>{ using type = delegate_category; };
    template <> struct category<winrt::Windows::Media::Capture::RecordLimitationExceededEventHandler>{ using type = delegate_category; };
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::AdvancedCapturedPhoto> = L"Windows.Media.Capture.AdvancedCapturedPhoto";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::AdvancedPhotoCapture> = L"Windows.Media.Capture.AdvancedPhotoCapture";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::AppCapture> = L"Windows.Media.Capture.AppCapture";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::CameraCaptureUI> = L"Windows.Media.Capture.CameraCaptureUI";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::CameraCaptureUIPhotoCaptureSettings> = L"Windows.Media.Capture.CameraCaptureUIPhotoCaptureSettings";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::CameraCaptureUIVideoCaptureSettings> = L"Windows.Media.Capture.CameraCaptureUIVideoCaptureSettings";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::CapturedFrame> = L"Windows.Media.Capture.CapturedFrame";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::CapturedFrameControlValues> = L"Windows.Media.Capture.CapturedFrameControlValues";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::CapturedPhoto> = L"Windows.Media.Capture.CapturedPhoto";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::LowLagMediaRecording> = L"Windows.Media.Capture.LowLagMediaRecording";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::LowLagPhotoCapture> = L"Windows.Media.Capture.LowLagPhotoCapture";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::LowLagPhotoSequenceCapture> = L"Windows.Media.Capture.LowLagPhotoSequenceCapture";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::MediaCapture> = L"Windows.Media.Capture.MediaCapture";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::MediaCaptureDeviceExclusiveControlStatusChangedEventArgs> = L"Windows.Media.Capture.MediaCaptureDeviceExclusiveControlStatusChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::MediaCaptureFailedEventArgs> = L"Windows.Media.Capture.MediaCaptureFailedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::MediaCaptureFocusChangedEventArgs> = L"Windows.Media.Capture.MediaCaptureFocusChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::MediaCaptureInitializationSettings> = L"Windows.Media.Capture.MediaCaptureInitializationSettings";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::MediaCapturePauseResult> = L"Windows.Media.Capture.MediaCapturePauseResult";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::MediaCaptureRelativePanelWatcher> = L"Windows.Media.Capture.MediaCaptureRelativePanelWatcher";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::MediaCaptureSettings> = L"Windows.Media.Capture.MediaCaptureSettings";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::MediaCaptureStopResult> = L"Windows.Media.Capture.MediaCaptureStopResult";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::MediaCaptureVideoProfile> = L"Windows.Media.Capture.MediaCaptureVideoProfile";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription> = L"Windows.Media.Capture.MediaCaptureVideoProfileMediaDescription";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::OptionalReferencePhotoCapturedEventArgs> = L"Windows.Media.Capture.OptionalReferencePhotoCapturedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::PhotoCapturedEventArgs> = L"Windows.Media.Capture.PhotoCapturedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::PhotoConfirmationCapturedEventArgs> = L"Windows.Media.Capture.PhotoConfirmationCapturedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::VideoStreamConfiguration> = L"Windows.Media.Capture.VideoStreamConfiguration";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::CameraCaptureUIMaxPhotoResolution> = L"Windows.Media.Capture.CameraCaptureUIMaxPhotoResolution";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::CameraCaptureUIMaxVideoResolution> = L"Windows.Media.Capture.CameraCaptureUIMaxVideoResolution";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::CameraCaptureUIMode> = L"Windows.Media.Capture.CameraCaptureUIMode";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::CameraCaptureUIPhotoFormat> = L"Windows.Media.Capture.CameraCaptureUIPhotoFormat";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::CameraCaptureUIVideoFormat> = L"Windows.Media.Capture.CameraCaptureUIVideoFormat";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::KnownVideoProfile> = L"Windows.Media.Capture.KnownVideoProfile";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::MediaCaptureDeviceExclusiveControlReleaseMode> = L"Windows.Media.Capture.MediaCaptureDeviceExclusiveControlReleaseMode";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::MediaCaptureDeviceExclusiveControlStatus> = L"Windows.Media.Capture.MediaCaptureDeviceExclusiveControlStatus";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::MediaCaptureMemoryPreference> = L"Windows.Media.Capture.MediaCaptureMemoryPreference";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::MediaCaptureSharingMode> = L"Windows.Media.Capture.MediaCaptureSharingMode";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::MediaCaptureThermalStatus> = L"Windows.Media.Capture.MediaCaptureThermalStatus";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::MediaCategory> = L"Windows.Media.Capture.MediaCategory";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::MediaStreamType> = L"Windows.Media.Capture.MediaStreamType";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::PhotoCaptureSource> = L"Windows.Media.Capture.PhotoCaptureSource";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::PowerlineFrequency> = L"Windows.Media.Capture.PowerlineFrequency";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::StreamingCaptureMode> = L"Windows.Media.Capture.StreamingCaptureMode";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::VideoDeviceCharacteristic> = L"Windows.Media.Capture.VideoDeviceCharacteristic";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::VideoRotation> = L"Windows.Media.Capture.VideoRotation";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::WhiteBalanceGain> = L"Windows.Media.Capture.WhiteBalanceGain";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IAdvancedCapturedPhoto> = L"Windows.Media.Capture.IAdvancedCapturedPhoto";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IAdvancedCapturedPhoto2> = L"Windows.Media.Capture.IAdvancedCapturedPhoto2";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IAdvancedPhotoCapture> = L"Windows.Media.Capture.IAdvancedPhotoCapture";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IAppCapture> = L"Windows.Media.Capture.IAppCapture";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IAppCaptureStatics> = L"Windows.Media.Capture.IAppCaptureStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IAppCaptureStatics2> = L"Windows.Media.Capture.IAppCaptureStatics2";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::ICameraCaptureUI> = L"Windows.Media.Capture.ICameraCaptureUI";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::ICameraCaptureUIPhotoCaptureSettings> = L"Windows.Media.Capture.ICameraCaptureUIPhotoCaptureSettings";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::ICameraCaptureUIVideoCaptureSettings> = L"Windows.Media.Capture.ICameraCaptureUIVideoCaptureSettings";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::ICapturedFrame> = L"Windows.Media.Capture.ICapturedFrame";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::ICapturedFrame2> = L"Windows.Media.Capture.ICapturedFrame2";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::ICapturedFrameControlValues> = L"Windows.Media.Capture.ICapturedFrameControlValues";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::ICapturedFrameControlValues2> = L"Windows.Media.Capture.ICapturedFrameControlValues2";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::ICapturedFrameWithSoftwareBitmap> = L"Windows.Media.Capture.ICapturedFrameWithSoftwareBitmap";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::ICapturedPhoto> = L"Windows.Media.Capture.ICapturedPhoto";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::ILowLagMediaRecording> = L"Windows.Media.Capture.ILowLagMediaRecording";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::ILowLagMediaRecording2> = L"Windows.Media.Capture.ILowLagMediaRecording2";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::ILowLagMediaRecording3> = L"Windows.Media.Capture.ILowLagMediaRecording3";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::ILowLagPhotoCapture> = L"Windows.Media.Capture.ILowLagPhotoCapture";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::ILowLagPhotoSequenceCapture> = L"Windows.Media.Capture.ILowLagPhotoSequenceCapture";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCapture> = L"Windows.Media.Capture.IMediaCapture";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCapture2> = L"Windows.Media.Capture.IMediaCapture2";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCapture3> = L"Windows.Media.Capture.IMediaCapture3";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCapture4> = L"Windows.Media.Capture.IMediaCapture4";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCapture5> = L"Windows.Media.Capture.IMediaCapture5";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCapture6> = L"Windows.Media.Capture.IMediaCapture6";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCapture7> = L"Windows.Media.Capture.IMediaCapture7";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCaptureDeviceExclusiveControlStatusChangedEventArgs> = L"Windows.Media.Capture.IMediaCaptureDeviceExclusiveControlStatusChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCaptureFailedEventArgs> = L"Windows.Media.Capture.IMediaCaptureFailedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCaptureFocusChangedEventArgs> = L"Windows.Media.Capture.IMediaCaptureFocusChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings> = L"Windows.Media.Capture.IMediaCaptureInitializationSettings";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings2> = L"Windows.Media.Capture.IMediaCaptureInitializationSettings2";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings3> = L"Windows.Media.Capture.IMediaCaptureInitializationSettings3";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings4> = L"Windows.Media.Capture.IMediaCaptureInitializationSettings4";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings5> = L"Windows.Media.Capture.IMediaCaptureInitializationSettings5";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings6> = L"Windows.Media.Capture.IMediaCaptureInitializationSettings6";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings7> = L"Windows.Media.Capture.IMediaCaptureInitializationSettings7";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCapturePauseResult> = L"Windows.Media.Capture.IMediaCapturePauseResult";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCaptureRelativePanelWatcher> = L"Windows.Media.Capture.IMediaCaptureRelativePanelWatcher";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCaptureSettings> = L"Windows.Media.Capture.IMediaCaptureSettings";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCaptureSettings2> = L"Windows.Media.Capture.IMediaCaptureSettings2";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCaptureSettings3> = L"Windows.Media.Capture.IMediaCaptureSettings3";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCaptureStatics> = L"Windows.Media.Capture.IMediaCaptureStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCaptureStopResult> = L"Windows.Media.Capture.IMediaCaptureStopResult";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCaptureVideoPreview> = L"Windows.Media.Capture.IMediaCaptureVideoPreview";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCaptureVideoProfile> = L"Windows.Media.Capture.IMediaCaptureVideoProfile";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCaptureVideoProfile2> = L"Windows.Media.Capture.IMediaCaptureVideoProfile2";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription> = L"Windows.Media.Capture.IMediaCaptureVideoProfileMediaDescription";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription2> = L"Windows.Media.Capture.IMediaCaptureVideoProfileMediaDescription2";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IOptionalReferencePhotoCapturedEventArgs> = L"Windows.Media.Capture.IOptionalReferencePhotoCapturedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IPhotoCapturedEventArgs> = L"Windows.Media.Capture.IPhotoCapturedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IPhotoConfirmationCapturedEventArgs> = L"Windows.Media.Capture.IPhotoConfirmationCapturedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::IVideoStreamConfiguration> = L"Windows.Media.Capture.IVideoStreamConfiguration";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::MediaCaptureFailedEventHandler> = L"Windows.Media.Capture.MediaCaptureFailedEventHandler";
    template <> inline constexpr auto& name_v<winrt::Windows::Media::Capture::RecordLimitationExceededEventHandler> = L"Windows.Media.Capture.RecordLimitationExceededEventHandler";
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IAdvancedCapturedPhoto>{ 0xF072728B,0xB292,0x4491,{ 0x9D,0x41,0x99,0x80,0x7A,0x55,0x0B,0xBF } }; // F072728B-B292-4491-9D41-99807A550BBF
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IAdvancedCapturedPhoto2>{ 0x18CF6CD8,0xCFFE,0x42D8,{ 0x81,0x04,0x01,0x7B,0xB3,0x18,0xF4,0xA1 } }; // 18CF6CD8-CFFE-42D8-8104-017BB318F4A1
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IAdvancedPhotoCapture>{ 0x83FFAAFA,0x6667,0x44DC,{ 0x97,0x3C,0xA6,0xBC,0xE5,0x96,0xAA,0x0F } }; // 83FFAAFA-6667-44DC-973C-A6BCE596AA0F
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IAppCapture>{ 0x9749D453,0xA29A,0x45ED,{ 0x8F,0x29,0x22,0xD0,0x99,0x42,0xCF,0xF7 } }; // 9749D453-A29A-45ED-8F29-22D09942CFF7
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IAppCaptureStatics>{ 0xF922DD6C,0x0A7E,0x4E74,{ 0x8B,0x20,0x9C,0x1F,0x90,0x2D,0x08,0xA1 } }; // F922DD6C-0A7E-4E74-8B20-9C1F902D08A1
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IAppCaptureStatics2>{ 0xB2D881D4,0x836C,0x4DA4,{ 0xAF,0xD7,0xFA,0xCC,0x04,0x1E,0x1C,0xF3 } }; // B2D881D4-836C-4DA4-AFD7-FACC041E1CF3
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::ICameraCaptureUI>{ 0x48587540,0x6F93,0x4BB4,{ 0xB8,0xF3,0xE8,0x9E,0x48,0x94,0x8C,0x91 } }; // 48587540-6F93-4BB4-B8F3-E89E48948C91
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::ICameraCaptureUIPhotoCaptureSettings>{ 0xB9F5BE97,0x3472,0x46A8,{ 0x8A,0x9E,0x04,0xCE,0x42,0xCC,0xC9,0x7D } }; // B9F5BE97-3472-46A8-8A9E-04CE42CCC97D
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::ICameraCaptureUIVideoCaptureSettings>{ 0x64E92D1F,0xA28D,0x425A,{ 0xB8,0x4F,0xE5,0x68,0x33,0x5F,0xF2,0x4E } }; // 64E92D1F-A28D-425A-B84F-E568335FF24E
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::ICapturedFrame>{ 0x1DD2DE1F,0x571B,0x44D8,{ 0x8E,0x80,0xA0,0x8A,0x15,0x78,0x76,0x6E } }; // 1DD2DE1F-571B-44D8-8E80-A08A1578766E
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::ICapturedFrame2>{ 0x543FA6D1,0xBD78,0x4866,{ 0xAD,0xDA,0x24,0x31,0x4B,0xC6,0x5D,0xEA } }; // 543FA6D1-BD78-4866-ADDA-24314BC65DEA
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::ICapturedFrameControlValues>{ 0x90C65B7F,0x4E0D,0x4CA4,{ 0x88,0x2D,0x7A,0x14,0x4F,0xED,0x0A,0x90 } }; // 90C65B7F-4E0D-4CA4-882D-7A144FED0A90
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::ICapturedFrameControlValues2>{ 0x500B2B88,0x06D2,0x4AA7,{ 0xA7,0xDB,0xD3,0x7A,0xF7,0x33,0x21,0xD8 } }; // 500B2B88-06D2-4AA7-A7DB-D37AF73321D8
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::ICapturedFrameWithSoftwareBitmap>{ 0xB58E8B6E,0x8503,0x49B5,{ 0x9E,0x86,0x89,0x7D,0x26,0xA3,0xFF,0x3D } }; // B58E8B6E-8503-49B5-9E86-897D26A3FF3D
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::ICapturedPhoto>{ 0xB0CE7E5A,0xCFCC,0x4D6C,{ 0x8A,0xD1,0x08,0x69,0x20,0x8A,0xCA,0x16 } }; // B0CE7E5A-CFCC-4D6C-8AD1-0869208ACA16
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::ILowLagMediaRecording>{ 0x41C8BAF7,0xFF3F,0x49F0,{ 0xA4,0x77,0xF1,0x95,0xE3,0xCE,0x51,0x08 } }; // 41C8BAF7-FF3F-49F0-A477-F195E3CE5108
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::ILowLagMediaRecording2>{ 0x6369C758,0x5644,0x41E2,{ 0x97,0xAF,0x8E,0xF5,0x6A,0x25,0xE2,0x25 } }; // 6369C758-5644-41E2-97AF-8EF56A25E225
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::ILowLagMediaRecording3>{ 0x5C33AB12,0x48F7,0x47DA,{ 0xB4,0x1E,0x90,0x88,0x0A,0x5F,0xE0,0xEC } }; // 5C33AB12-48F7-47DA-B41E-90880A5FE0EC
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::ILowLagPhotoCapture>{ 0xA37251B7,0x6B44,0x473D,{ 0x8F,0x24,0xF7,0x03,0xD6,0xC0,0xEC,0x44 } }; // A37251B7-6B44-473D-8F24-F703D6C0EC44
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::ILowLagPhotoSequenceCapture>{ 0x7CC346BB,0xB9A9,0x4C91,{ 0x8F,0xFA,0x28,0x7E,0x9C,0x66,0x86,0x69 } }; // 7CC346BB-B9A9-4C91-8FFA-287E9C668669
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCapture>{ 0xC61AFBB4,0xFB10,0x4A34,{ 0xAC,0x18,0xCA,0x80,0xD9,0xC8,0xE7,0xEE } }; // C61AFBB4-FB10-4A34-AC18-CA80D9C8E7EE
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCapture2>{ 0x9CC68260,0x7DA1,0x4043,{ 0xB6,0x52,0x21,0xB8,0x87,0x8D,0xAF,0xF9 } }; // 9CC68260-7DA1-4043-B652-21B8878DAFF9
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCapture3>{ 0xD4136F30,0x1564,0x466E,{ 0xBC,0x0A,0xAF,0x94,0xE0,0x2A,0xB0,0x16 } }; // D4136F30-1564-466E-BC0A-AF94E02AB016
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCapture4>{ 0xBACD6FD6,0xFB08,0x4947,{ 0xAE,0xA2,0xCE,0x14,0xEF,0xF0,0xCE,0x13 } }; // BACD6FD6-FB08-4947-AEA2-CE14EFF0CE13
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCapture5>{ 0xDA787C22,0x3A9B,0x4720,{ 0xA7,0x1E,0x97,0x90,0x0A,0x31,0x6E,0x5A } }; // DA787C22-3A9B-4720-A71E-97900A316E5A
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCapture6>{ 0x228948BD,0x4B20,0x4BB1,{ 0x9F,0xD6,0xA5,0x83,0x21,0x2A,0x10,0x12 } }; // 228948BD-4B20-4BB1-9FD6-A583212A1012
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCapture7>{ 0x9169F102,0x8888,0x541A,{ 0x95,0xBC,0x24,0xE4,0xD4,0x62,0x54,0x2A } }; // 9169F102-8888-541A-95BC-24E4D462542A
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCaptureDeviceExclusiveControlStatusChangedEventArgs>{ 0x9D2F920D,0xA588,0x43C6,{ 0x89,0xD6,0x5A,0xD3,0x22,0xAF,0x00,0x6A } }; // 9D2F920D-A588-43C6-89D6-5AD322AF006A
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCaptureFailedEventArgs>{ 0x80FDE3F4,0x54C4,0x42C0,{ 0x8D,0x19,0xCE,0xA1,0xA8,0x7C,0xA1,0x8B } }; // 80FDE3F4-54C4-42C0-8D19-CEA1A87CA18B
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCaptureFocusChangedEventArgs>{ 0x81E1BC7F,0x2277,0x493E,{ 0xAB,0xEE,0xD3,0xF4,0x4F,0xF9,0x8C,0x04 } }; // 81E1BC7F-2277-493E-ABEE-D3F44FF98C04
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings>{ 0x9782BA70,0xEA65,0x4900,{ 0x93,0x56,0x8C,0xA8,0x87,0x72,0x68,0x84 } }; // 9782BA70-EA65-4900-9356-8CA887726884
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings2>{ 0x404E0626,0xC9DC,0x43E9,{ 0xAE,0xE4,0xE6,0xBF,0x1B,0x57,0xB4,0x4C } }; // 404E0626-C9DC-43E9-AEE4-E6BF1B57B44C
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings3>{ 0x4160519D,0xBE48,0x4730,{ 0x81,0x04,0x0C,0xF6,0xE9,0xE9,0x79,0x48 } }; // 4160519D-BE48-4730-8104-0CF6E9E97948
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings4>{ 0xF502A537,0x4CB7,0x4D28,{ 0x95,0xED,0x4F,0x9F,0x01,0x2E,0x05,0x18 } }; // F502A537-4CB7-4D28-95ED-4F9F012E0518
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings5>{ 0xD5A2E3B8,0x2626,0x4E94,{ 0xB7,0xB3,0x53,0x08,0xA0,0xF6,0x4B,0x1A } }; // D5A2E3B8-2626-4E94-B7B3-5308A0F64B1A
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings6>{ 0xB2E26B47,0x3DB1,0x4D33,{ 0xAB,0x63,0x0F,0xFA,0x09,0x05,0x65,0x85 } }; // B2E26B47-3DB1-4D33-AB63-0FFA09056585
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings7>{ 0x41546967,0xF58A,0x5D82,{ 0x9E,0xF4,0xED,0x57,0x2F,0xB5,0xE3,0x4E } }; // 41546967-F58A-5D82-9EF4-ED572FB5E34E
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCapturePauseResult>{ 0xAEC47CA3,0x4477,0x4B04,{ 0xA0,0x6F,0x2C,0x1C,0x51,0x82,0xFE,0x9D } }; // AEC47CA3-4477-4B04-A06F-2C1C5182FE9D
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCaptureRelativePanelWatcher>{ 0x7D896566,0x04BE,0x5B89,{ 0xB3,0x0E,0xBD,0x34,0xA9,0xF1,0x2D,0xB0 } }; // 7D896566-04BE-5B89-B30E-BD34A9F12DB0
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCaptureSettings>{ 0x1D83AAFE,0x6D45,0x4477,{ 0x8D,0xC4,0xAC,0x5B,0xC0,0x1C,0x40,0x91 } }; // 1D83AAFE-6D45-4477-8DC4-AC5BC01C4091
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCaptureSettings2>{ 0x6F9E7CFB,0xFA9F,0x4B13,{ 0x9C,0xBE,0x5A,0xB9,0x4F,0x1F,0x34,0x93 } }; // 6F9E7CFB-FA9F-4B13-9CBE-5AB94F1F3493
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCaptureSettings3>{ 0x303C67C2,0x8058,0x4B1B,{ 0xB8,0x77,0x8C,0x2E,0xF3,0x52,0x84,0x40 } }; // 303C67C2-8058-4B1B-B877-8C2EF3528440
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCaptureStatics>{ 0xACEF81FF,0x99ED,0x4645,{ 0x96,0x5E,0x19,0x25,0xCF,0xC6,0x38,0x34 } }; // ACEF81FF-99ED-4645-965E-1925CFC63834
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCaptureStopResult>{ 0xF9DB6A2A,0xA092,0x4AD1,{ 0x97,0xD4,0xF2,0x01,0xF9,0xD0,0x82,0xDB } }; // F9DB6A2A-A092-4AD1-97D4-F201F9D082DB
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCaptureVideoPreview>{ 0x27727073,0x549E,0x447F,{ 0xA2,0x0A,0x4F,0x03,0xC4,0x79,0xD8,0xC0 } }; // 27727073-549E-447F-A20A-4F03C479D8C0
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCaptureVideoProfile>{ 0x21A073BF,0xA3EE,0x4ECF,{ 0x9E,0xF6,0x50,0xB0,0xBC,0x4E,0x13,0x05 } }; // 21A073BF-A3EE-4ECF-9EF6-50B0BC4E1305
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCaptureVideoProfile2>{ 0x97DDC95F,0x94CE,0x468F,{ 0x93,0x16,0xFC,0x5B,0xC2,0x63,0x8F,0x6B } }; // 97DDC95F-94CE-468F-9316-FC5BC2638F6B
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription>{ 0x8012AFEF,0xB691,0x49FF,{ 0x83,0xF2,0xC1,0xE7,0x6E,0xAA,0xEA,0x1B } }; // 8012AFEF-B691-49FF-83F2-C1E76EAAEA1B
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription2>{ 0xC6A6EF13,0x322D,0x413A,{ 0xB8,0x5A,0x68,0xA8,0x8E,0x02,0xF4,0xE9 } }; // C6A6EF13-322D-413A-B85A-68A88E02F4E9
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IOptionalReferencePhotoCapturedEventArgs>{ 0x470F88B3,0x1E6D,0x4051,{ 0x9C,0x8B,0xF1,0xD8,0x5A,0xF0,0x47,0xB7 } }; // 470F88B3-1E6D-4051-9C8B-F1D85AF047B7
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IPhotoCapturedEventArgs>{ 0x373BFBC1,0x984E,0x4FF0,{ 0xBF,0x85,0x1C,0x00,0xAA,0xBC,0x5A,0x45 } }; // 373BFBC1-984E-4FF0-BF85-1C00AABC5A45
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IPhotoConfirmationCapturedEventArgs>{ 0xAB473672,0xC28A,0x4827,{ 0x8F,0x8D,0x36,0x36,0xD3,0xBE,0xB5,0x1E } }; // AB473672-C28A-4827-8F8D-3636D3BEB51E
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::IVideoStreamConfiguration>{ 0xD8770A6F,0x4390,0x4B5E,{ 0xAD,0x3E,0x0F,0x8A,0xF0,0x96,0x34,0x90 } }; // D8770A6F-4390-4B5E-AD3E-0F8AF0963490
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::MediaCaptureFailedEventHandler>{ 0x2014EFFB,0x5CD8,0x4F08,{ 0xA3,0x14,0x0D,0x36,0x0D,0xA5,0x9F,0x14 } }; // 2014EFFB-5CD8-4F08-A314-0D360DA59F14
    template <> inline constexpr guid guid_v<winrt::Windows::Media::Capture::RecordLimitationExceededEventHandler>{ 0x3FAE8F2E,0x4FE1,0x4FFD,{ 0xAA,0xBA,0xE1,0xF1,0x33,0x7D,0x4E,0x53 } }; // 3FAE8F2E-4FE1-4FFD-AABA-E1F1337D4E53
    template <> struct default_interface<winrt::Windows::Media::Capture::AdvancedCapturedPhoto>{ using type = winrt::Windows::Media::Capture::IAdvancedCapturedPhoto; };
    template <> struct default_interface<winrt::Windows::Media::Capture::AdvancedPhotoCapture>{ using type = winrt::Windows::Media::Capture::IAdvancedPhotoCapture; };
    template <> struct default_interface<winrt::Windows::Media::Capture::AppCapture>{ using type = winrt::Windows::Media::Capture::IAppCapture; };
    template <> struct default_interface<winrt::Windows::Media::Capture::CameraCaptureUI>{ using type = winrt::Windows::Media::Capture::ICameraCaptureUI; };
    template <> struct default_interface<winrt::Windows::Media::Capture::CameraCaptureUIPhotoCaptureSettings>{ using type = winrt::Windows::Media::Capture::ICameraCaptureUIPhotoCaptureSettings; };
    template <> struct default_interface<winrt::Windows::Media::Capture::CameraCaptureUIVideoCaptureSettings>{ using type = winrt::Windows::Media::Capture::ICameraCaptureUIVideoCaptureSettings; };
    template <> struct default_interface<winrt::Windows::Media::Capture::CapturedFrame>{ using type = winrt::Windows::Media::Capture::ICapturedFrame; };
    template <> struct default_interface<winrt::Windows::Media::Capture::CapturedFrameControlValues>{ using type = winrt::Windows::Media::Capture::ICapturedFrameControlValues; };
    template <> struct default_interface<winrt::Windows::Media::Capture::CapturedPhoto>{ using type = winrt::Windows::Media::Capture::ICapturedPhoto; };
    template <> struct default_interface<winrt::Windows::Media::Capture::LowLagMediaRecording>{ using type = winrt::Windows::Media::Capture::ILowLagMediaRecording; };
    template <> struct default_interface<winrt::Windows::Media::Capture::LowLagPhotoCapture>{ using type = winrt::Windows::Media::Capture::ILowLagPhotoCapture; };
    template <> struct default_interface<winrt::Windows::Media::Capture::LowLagPhotoSequenceCapture>{ using type = winrt::Windows::Media::Capture::ILowLagPhotoSequenceCapture; };
    template <> struct default_interface<winrt::Windows::Media::Capture::MediaCapture>{ using type = winrt::Windows::Media::Capture::IMediaCapture; };
    template <> struct default_interface<winrt::Windows::Media::Capture::MediaCaptureDeviceExclusiveControlStatusChangedEventArgs>{ using type = winrt::Windows::Media::Capture::IMediaCaptureDeviceExclusiveControlStatusChangedEventArgs; };
    template <> struct default_interface<winrt::Windows::Media::Capture::MediaCaptureFailedEventArgs>{ using type = winrt::Windows::Media::Capture::IMediaCaptureFailedEventArgs; };
    template <> struct default_interface<winrt::Windows::Media::Capture::MediaCaptureFocusChangedEventArgs>{ using type = winrt::Windows::Media::Capture::IMediaCaptureFocusChangedEventArgs; };
    template <> struct default_interface<winrt::Windows::Media::Capture::MediaCaptureInitializationSettings>{ using type = winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings; };
    template <> struct default_interface<winrt::Windows::Media::Capture::MediaCapturePauseResult>{ using type = winrt::Windows::Media::Capture::IMediaCapturePauseResult; };
    template <> struct default_interface<winrt::Windows::Media::Capture::MediaCaptureRelativePanelWatcher>{ using type = winrt::Windows::Media::Capture::IMediaCaptureRelativePanelWatcher; };
    template <> struct default_interface<winrt::Windows::Media::Capture::MediaCaptureSettings>{ using type = winrt::Windows::Media::Capture::IMediaCaptureSettings; };
    template <> struct default_interface<winrt::Windows::Media::Capture::MediaCaptureStopResult>{ using type = winrt::Windows::Media::Capture::IMediaCaptureStopResult; };
    template <> struct default_interface<winrt::Windows::Media::Capture::MediaCaptureVideoProfile>{ using type = winrt::Windows::Media::Capture::IMediaCaptureVideoProfile; };
    template <> struct default_interface<winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription>{ using type = winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription; };
    template <> struct default_interface<winrt::Windows::Media::Capture::OptionalReferencePhotoCapturedEventArgs>{ using type = winrt::Windows::Media::Capture::IOptionalReferencePhotoCapturedEventArgs; };
    template <> struct default_interface<winrt::Windows::Media::Capture::PhotoCapturedEventArgs>{ using type = winrt::Windows::Media::Capture::IPhotoCapturedEventArgs; };
    template <> struct default_interface<winrt::Windows::Media::Capture::PhotoConfirmationCapturedEventArgs>{ using type = winrt::Windows::Media::Capture::IPhotoConfirmationCapturedEventArgs; };
    template <> struct default_interface<winrt::Windows::Media::Capture::VideoStreamConfiguration>{ using type = winrt::Windows::Media::Capture::IVideoStreamConfiguration; };
    template <> struct abi<winrt::Windows::Media::Capture::IAdvancedCapturedPhoto>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Frame(void**) noexcept = 0;
            virtual int32_t __stdcall get_Mode(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_Context(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IAdvancedCapturedPhoto2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_FrameBoundsRelativeToReferencePhoto(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IAdvancedPhotoCapture>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall CaptureAsync(void**) noexcept = 0;
            virtual int32_t __stdcall CaptureWithContextAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall add_OptionalReferencePhotoCaptured(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_OptionalReferencePhotoCaptured(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_AllPhotosCaptured(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_AllPhotosCaptured(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall FinishAsync(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IAppCapture>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsCapturingAudio(bool*) noexcept = 0;
            virtual int32_t __stdcall get_IsCapturingVideo(bool*) noexcept = 0;
            virtual int32_t __stdcall add_CapturingChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_CapturingChanged(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IAppCaptureStatics>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall GetForCurrentView(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IAppCaptureStatics2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall SetAllowedAsync(bool, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::ICameraCaptureUI>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_PhotoSettings(void**) noexcept = 0;
            virtual int32_t __stdcall get_VideoSettings(void**) noexcept = 0;
            virtual int32_t __stdcall CaptureFileAsync(int32_t, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::ICameraCaptureUIPhotoCaptureSettings>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Format(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_Format(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_MaxResolution(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_MaxResolution(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_CroppedSizeInPixels(winrt::Windows::Foundation::Size*) noexcept = 0;
            virtual int32_t __stdcall put_CroppedSizeInPixels(winrt::Windows::Foundation::Size) noexcept = 0;
            virtual int32_t __stdcall get_CroppedAspectRatio(winrt::Windows::Foundation::Size*) noexcept = 0;
            virtual int32_t __stdcall put_CroppedAspectRatio(winrt::Windows::Foundation::Size) noexcept = 0;
            virtual int32_t __stdcall get_AllowCropping(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AllowCropping(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::ICameraCaptureUIVideoCaptureSettings>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Format(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_Format(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_MaxResolution(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_MaxResolution(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_MaxDurationInSeconds(float*) noexcept = 0;
            virtual int32_t __stdcall put_MaxDurationInSeconds(float) noexcept = 0;
            virtual int32_t __stdcall get_AllowTrimming(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AllowTrimming(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::ICapturedFrame>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Width(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall get_Height(uint32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::ICapturedFrame2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_ControlValues(void**) noexcept = 0;
            virtual int32_t __stdcall get_BitmapProperties(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::ICapturedFrameControlValues>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Exposure(void**) noexcept = 0;
            virtual int32_t __stdcall get_ExposureCompensation(void**) noexcept = 0;
            virtual int32_t __stdcall get_IsoSpeed(void**) noexcept = 0;
            virtual int32_t __stdcall get_Focus(void**) noexcept = 0;
            virtual int32_t __stdcall get_SceneMode(void**) noexcept = 0;
            virtual int32_t __stdcall get_Flashed(void**) noexcept = 0;
            virtual int32_t __stdcall get_FlashPowerPercent(void**) noexcept = 0;
            virtual int32_t __stdcall get_WhiteBalance(void**) noexcept = 0;
            virtual int32_t __stdcall get_ZoomFactor(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::ICapturedFrameControlValues2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_FocusState(void**) noexcept = 0;
            virtual int32_t __stdcall get_IsoDigitalGain(void**) noexcept = 0;
            virtual int32_t __stdcall get_IsoAnalogGain(void**) noexcept = 0;
            virtual int32_t __stdcall get_SensorFrameRate(void**) noexcept = 0;
            virtual int32_t __stdcall get_WhiteBalanceGain(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::ICapturedFrameWithSoftwareBitmap>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_SoftwareBitmap(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::ICapturedPhoto>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Frame(void**) noexcept = 0;
            virtual int32_t __stdcall get_Thumbnail(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::ILowLagMediaRecording>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall StartAsync(void**) noexcept = 0;
            virtual int32_t __stdcall StopAsync(void**) noexcept = 0;
            virtual int32_t __stdcall FinishAsync(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::ILowLagMediaRecording2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall PauseAsync(int32_t, void**) noexcept = 0;
            virtual int32_t __stdcall ResumeAsync(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::ILowLagMediaRecording3>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall PauseWithResultAsync(int32_t, void**) noexcept = 0;
            virtual int32_t __stdcall StopWithResultAsync(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::ILowLagPhotoCapture>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall CaptureAsync(void**) noexcept = 0;
            virtual int32_t __stdcall FinishAsync(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::ILowLagPhotoSequenceCapture>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall StartAsync(void**) noexcept = 0;
            virtual int32_t __stdcall StopAsync(void**) noexcept = 0;
            virtual int32_t __stdcall FinishAsync(void**) noexcept = 0;
            virtual int32_t __stdcall add_PhotoCaptured(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_PhotoCaptured(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCapture>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall InitializeAsync(void**) noexcept = 0;
            virtual int32_t __stdcall InitializeWithSettingsAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall StartRecordToStorageFileAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall StartRecordToStreamAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall StartRecordToCustomSinkAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall StartRecordToCustomSinkIdAsync(void*, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall StopRecordAsync(void**) noexcept = 0;
            virtual int32_t __stdcall CapturePhotoToStorageFileAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall CapturePhotoToStreamAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall AddEffectAsync(int32_t, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall ClearEffectsAsync(int32_t, void**) noexcept = 0;
            virtual int32_t __stdcall SetEncoderProperty(int32_t, winrt::guid, void*) noexcept = 0;
            virtual int32_t __stdcall GetEncoderProperty(int32_t, winrt::guid, void**) noexcept = 0;
            virtual int32_t __stdcall add_Failed(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Failed(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_RecordLimitationExceeded(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_RecordLimitationExceeded(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall get_MediaCaptureSettings(void**) noexcept = 0;
            virtual int32_t __stdcall get_AudioDeviceController(void**) noexcept = 0;
            virtual int32_t __stdcall get_VideoDeviceController(void**) noexcept = 0;
            virtual int32_t __stdcall SetPreviewMirroring(bool) noexcept = 0;
            virtual int32_t __stdcall GetPreviewMirroring(bool*) noexcept = 0;
            virtual int32_t __stdcall SetPreviewRotation(int32_t) noexcept = 0;
            virtual int32_t __stdcall GetPreviewRotation(int32_t*) noexcept = 0;
            virtual int32_t __stdcall SetRecordRotation(int32_t) noexcept = 0;
            virtual int32_t __stdcall GetRecordRotation(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCapture2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall PrepareLowLagRecordToStorageFileAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall PrepareLowLagRecordToStreamAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall PrepareLowLagRecordToCustomSinkAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall PrepareLowLagRecordToCustomSinkIdAsync(void*, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall PrepareLowLagPhotoCaptureAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall PrepareLowLagPhotoSequenceCaptureAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall SetEncodingPropertiesAsync(int32_t, void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCapture3>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall PrepareVariablePhotoSequenceCaptureAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall add_FocusChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_FocusChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_PhotoConfirmationCaptured(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_PhotoConfirmationCaptured(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCapture4>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall AddAudioEffectAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall AddVideoEffectAsync(void*, int32_t, void**) noexcept = 0;
            virtual int32_t __stdcall PauseRecordAsync(int32_t, void**) noexcept = 0;
            virtual int32_t __stdcall ResumeRecordAsync(void**) noexcept = 0;
            virtual int32_t __stdcall add_CameraStreamStateChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_CameraStreamStateChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall get_CameraStreamState(int32_t*) noexcept = 0;
            virtual int32_t __stdcall GetPreviewFrameAsync(void**) noexcept = 0;
            virtual int32_t __stdcall GetPreviewFrameCopyAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall add_ThermalStatusChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ThermalStatusChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall get_ThermalStatus(int32_t*) noexcept = 0;
            virtual int32_t __stdcall PrepareAdvancedPhotoCaptureAsync(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCapture5>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall RemoveEffectAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall PauseRecordWithResultAsync(int32_t, void**) noexcept = 0;
            virtual int32_t __stdcall StopRecordWithResultAsync(void**) noexcept = 0;
            virtual int32_t __stdcall get_FrameSources(void**) noexcept = 0;
            virtual int32_t __stdcall CreateFrameReaderAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall CreateFrameReaderWithSubtypeAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall CreateFrameReaderWithSubtypeAndSizeAsync(void*, void*, struct struct_Windows_Graphics_Imaging_BitmapSize, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCapture6>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall add_CaptureDeviceExclusiveControlStatusChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_CaptureDeviceExclusiveControlStatusChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall CreateMultiSourceFrameReaderAsync(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCapture7>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall CreateRelativePanelWatcher(int32_t, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCaptureDeviceExclusiveControlStatusChangedEventArgs>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_DeviceId(void**) noexcept = 0;
            virtual int32_t __stdcall get_Status(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCaptureFailedEventArgs>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Message(void**) noexcept = 0;
            virtual int32_t __stdcall get_Code(uint32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCaptureFocusChangedEventArgs>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_FocusState(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall put_AudioDeviceId(void*) noexcept = 0;
            virtual int32_t __stdcall get_AudioDeviceId(void**) noexcept = 0;
            virtual int32_t __stdcall put_VideoDeviceId(void*) noexcept = 0;
            virtual int32_t __stdcall get_VideoDeviceId(void**) noexcept = 0;
            virtual int32_t __stdcall put_StreamingCaptureMode(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_StreamingCaptureMode(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_PhotoCaptureSource(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_PhotoCaptureSource(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall put_MediaCategory(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_MediaCategory(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_AudioProcessing(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_AudioProcessing(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings3>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall put_AudioSource(void*) noexcept = 0;
            virtual int32_t __stdcall get_AudioSource(void**) noexcept = 0;
            virtual int32_t __stdcall put_VideoSource(void*) noexcept = 0;
            virtual int32_t __stdcall get_VideoSource(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings4>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_VideoProfile(void**) noexcept = 0;
            virtual int32_t __stdcall put_VideoProfile(void*) noexcept = 0;
            virtual int32_t __stdcall get_PreviewMediaDescription(void**) noexcept = 0;
            virtual int32_t __stdcall put_PreviewMediaDescription(void*) noexcept = 0;
            virtual int32_t __stdcall get_RecordMediaDescription(void**) noexcept = 0;
            virtual int32_t __stdcall put_RecordMediaDescription(void*) noexcept = 0;
            virtual int32_t __stdcall get_PhotoMediaDescription(void**) noexcept = 0;
            virtual int32_t __stdcall put_PhotoMediaDescription(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings5>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_SourceGroup(void**) noexcept = 0;
            virtual int32_t __stdcall put_SourceGroup(void*) noexcept = 0;
            virtual int32_t __stdcall get_SharingMode(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_SharingMode(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_MemoryPreference(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_MemoryPreference(int32_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings6>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_AlwaysPlaySystemShutterSound(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AlwaysPlaySystemShutterSound(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings7>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_DeviceUriPasswordCredential(void**) noexcept = 0;
            virtual int32_t __stdcall put_DeviceUriPasswordCredential(void*) noexcept = 0;
            virtual int32_t __stdcall get_DeviceUri(void**) noexcept = 0;
            virtual int32_t __stdcall put_DeviceUri(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCapturePauseResult>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_LastFrame(void**) noexcept = 0;
            virtual int32_t __stdcall get_RecordDuration(int64_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCaptureRelativePanelWatcher>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_RelativePanel(int32_t*) noexcept = 0;
            virtual int32_t __stdcall add_Changed(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Changed(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall Start() noexcept = 0;
            virtual int32_t __stdcall Stop() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCaptureSettings>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_AudioDeviceId(void**) noexcept = 0;
            virtual int32_t __stdcall get_VideoDeviceId(void**) noexcept = 0;
            virtual int32_t __stdcall get_StreamingCaptureMode(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_PhotoCaptureSource(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_VideoDeviceCharacteristic(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCaptureSettings2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_ConcurrentRecordAndPhotoSupported(bool*) noexcept = 0;
            virtual int32_t __stdcall get_ConcurrentRecordAndPhotoSequenceSupported(bool*) noexcept = 0;
            virtual int32_t __stdcall get_CameraSoundRequiredForRegion(bool*) noexcept = 0;
            virtual int32_t __stdcall get_Horizontal35mmEquivalentFocalLength(void**) noexcept = 0;
            virtual int32_t __stdcall get_PitchOffsetDegrees(void**) noexcept = 0;
            virtual int32_t __stdcall get_Vertical35mmEquivalentFocalLength(void**) noexcept = 0;
            virtual int32_t __stdcall get_MediaCategory(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_AudioProcessing(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCaptureSettings3>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Direct3D11Device(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCaptureStatics>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall IsVideoProfileSupported(void*, bool*) noexcept = 0;
            virtual int32_t __stdcall FindAllVideoProfiles(void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindConcurrentProfiles(void*, void**) noexcept = 0;
            virtual int32_t __stdcall FindKnownVideoProfiles(void*, int32_t, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCaptureStopResult>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_LastFrame(void**) noexcept = 0;
            virtual int32_t __stdcall get_RecordDuration(int64_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCaptureVideoPreview>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall StartPreviewAsync(void**) noexcept = 0;
            virtual int32_t __stdcall StartPreviewToCustomSinkAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall StartPreviewToCustomSinkIdAsync(void*, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall StopPreviewAsync(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCaptureVideoProfile>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Id(void**) noexcept = 0;
            virtual int32_t __stdcall get_VideoDeviceId(void**) noexcept = 0;
            virtual int32_t __stdcall get_SupportedPreviewMediaDescription(void**) noexcept = 0;
            virtual int32_t __stdcall get_SupportedRecordMediaDescription(void**) noexcept = 0;
            virtual int32_t __stdcall get_SupportedPhotoMediaDescription(void**) noexcept = 0;
            virtual int32_t __stdcall GetConcurrency(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCaptureVideoProfile2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_FrameSourceInfos(void**) noexcept = 0;
            virtual int32_t __stdcall get_Properties(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Width(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall get_Height(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall get_FrameRate(double*) noexcept = 0;
            virtual int32_t __stdcall get_IsVariablePhotoSequenceSupported(bool*) noexcept = 0;
            virtual int32_t __stdcall get_IsHdrVideoSupported(bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Subtype(void**) noexcept = 0;
            virtual int32_t __stdcall get_Properties(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IOptionalReferencePhotoCapturedEventArgs>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Frame(void**) noexcept = 0;
            virtual int32_t __stdcall get_Context(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IPhotoCapturedEventArgs>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Frame(void**) noexcept = 0;
            virtual int32_t __stdcall get_Thumbnail(void**) noexcept = 0;
            virtual int32_t __stdcall get_CaptureTimeOffset(int64_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IPhotoConfirmationCapturedEventArgs>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Frame(void**) noexcept = 0;
            virtual int32_t __stdcall get_CaptureTimeOffset(int64_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::IVideoStreamConfiguration>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_InputProperties(void**) noexcept = 0;
            virtual int32_t __stdcall get_OutputProperties(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::MediaCaptureFailedEventHandler>
    {
        struct __declspec(novtable) type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Media::Capture::RecordLimitationExceededEventHandler>
    {
        struct __declspec(novtable) type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*) noexcept = 0;
        };
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IAdvancedCapturedPhoto
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::CapturedFrame) Frame() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Devices::AdvancedPhotoMode) Mode() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::IInspectable) Context() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IAdvancedCapturedPhoto>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IAdvancedCapturedPhoto<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IAdvancedCapturedPhoto2
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::IReference<winrt::Windows::Foundation::Rect>) FrameBoundsRelativeToReferencePhoto() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IAdvancedCapturedPhoto2>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IAdvancedCapturedPhoto2<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IAdvancedPhotoCapture
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::AdvancedCapturedPhoto>) CaptureAsync() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::AdvancedCapturedPhoto>) CaptureAsync(winrt::Windows::Foundation::IInspectable const& context) const;
        WINRT_IMPL_AUTO(winrt::event_token) OptionalReferencePhotoCaptured(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::AdvancedPhotoCapture, winrt::Windows::Media::Capture::OptionalReferencePhotoCapturedEventArgs> const& handler) const;
        using OptionalReferencePhotoCaptured_revoker = impl::event_revoker<winrt::Windows::Media::Capture::IAdvancedPhotoCapture, &impl::abi_t<winrt::Windows::Media::Capture::IAdvancedPhotoCapture>::remove_OptionalReferencePhotoCaptured>;
        [[nodiscard]] OptionalReferencePhotoCaptured_revoker OptionalReferencePhotoCaptured(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::AdvancedPhotoCapture, winrt::Windows::Media::Capture::OptionalReferencePhotoCapturedEventArgs> const& handler) const;
        WINRT_IMPL_AUTO(void) OptionalReferencePhotoCaptured(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) AllPhotosCaptured(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::AdvancedPhotoCapture, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using AllPhotosCaptured_revoker = impl::event_revoker<winrt::Windows::Media::Capture::IAdvancedPhotoCapture, &impl::abi_t<winrt::Windows::Media::Capture::IAdvancedPhotoCapture>::remove_AllPhotosCaptured>;
        [[nodiscard]] AllPhotosCaptured_revoker AllPhotosCaptured(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::AdvancedPhotoCapture, winrt::Windows::Foundation::IInspectable> const& handler) const;
        WINRT_IMPL_AUTO(void) AllPhotosCaptured(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) FinishAsync() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IAdvancedPhotoCapture>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IAdvancedPhotoCapture<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IAppCapture
    {
        [[nodiscard]] WINRT_IMPL_AUTO(bool) IsCapturingAudio() const;
        [[nodiscard]] WINRT_IMPL_AUTO(bool) IsCapturingVideo() const;
        WINRT_IMPL_AUTO(winrt::event_token) CapturingChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::AppCapture, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using CapturingChanged_revoker = impl::event_revoker<winrt::Windows::Media::Capture::IAppCapture, &impl::abi_t<winrt::Windows::Media::Capture::IAppCapture>::remove_CapturingChanged>;
        [[nodiscard]] CapturingChanged_revoker CapturingChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::AppCapture, winrt::Windows::Foundation::IInspectable> const& handler) const;
        WINRT_IMPL_AUTO(void) CapturingChanged(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IAppCapture>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IAppCapture<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IAppCaptureStatics
    {
        WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::AppCapture) GetForCurrentView() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IAppCaptureStatics>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IAppCaptureStatics<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IAppCaptureStatics2
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) SetAllowedAsync(bool allowed) const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IAppCaptureStatics2>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IAppCaptureStatics2<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_ICameraCaptureUI
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::CameraCaptureUIPhotoCaptureSettings) PhotoSettings() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::CameraCaptureUIVideoCaptureSettings) VideoSettings() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Storage::StorageFile>) CaptureFileAsync(winrt::Windows::Media::Capture::CameraCaptureUIMode const& mode) const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::ICameraCaptureUI>
    {
        template <typename D> using type = consume_Windows_Media_Capture_ICameraCaptureUI<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_ICameraCaptureUIPhotoCaptureSettings
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::CameraCaptureUIPhotoFormat) Format() const;
        WINRT_IMPL_AUTO(void) Format(winrt::Windows::Media::Capture::CameraCaptureUIPhotoFormat const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::CameraCaptureUIMaxPhotoResolution) MaxResolution() const;
        WINRT_IMPL_AUTO(void) MaxResolution(winrt::Windows::Media::Capture::CameraCaptureUIMaxPhotoResolution const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::Size) CroppedSizeInPixels() const;
        WINRT_IMPL_AUTO(void) CroppedSizeInPixels(winrt::Windows::Foundation::Size const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::Size) CroppedAspectRatio() const;
        WINRT_IMPL_AUTO(void) CroppedAspectRatio(winrt::Windows::Foundation::Size const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(bool) AllowCropping() const;
        WINRT_IMPL_AUTO(void) AllowCropping(bool value) const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::ICameraCaptureUIPhotoCaptureSettings>
    {
        template <typename D> using type = consume_Windows_Media_Capture_ICameraCaptureUIPhotoCaptureSettings<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_ICameraCaptureUIVideoCaptureSettings
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::CameraCaptureUIVideoFormat) Format() const;
        WINRT_IMPL_AUTO(void) Format(winrt::Windows::Media::Capture::CameraCaptureUIVideoFormat const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::CameraCaptureUIMaxVideoResolution) MaxResolution() const;
        WINRT_IMPL_AUTO(void) MaxResolution(winrt::Windows::Media::Capture::CameraCaptureUIMaxVideoResolution const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(float) MaxDurationInSeconds() const;
        WINRT_IMPL_AUTO(void) MaxDurationInSeconds(float value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(bool) AllowTrimming() const;
        WINRT_IMPL_AUTO(void) AllowTrimming(bool value) const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::ICameraCaptureUIVideoCaptureSettings>
    {
        template <typename D> using type = consume_Windows_Media_Capture_ICameraCaptureUIVideoCaptureSettings<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_ICapturedFrame
    {
        [[nodiscard]] WINRT_IMPL_AUTO(uint32_t) Width() const;
        [[nodiscard]] WINRT_IMPL_AUTO(uint32_t) Height() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::ICapturedFrame>
    {
        template <typename D> using type = consume_Windows_Media_Capture_ICapturedFrame<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_ICapturedFrame2
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::CapturedFrameControlValues) ControlValues() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Graphics::Imaging::BitmapPropertySet) BitmapProperties() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::ICapturedFrame2>
    {
        template <typename D> using type = consume_Windows_Media_Capture_ICapturedFrame2<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_ICapturedFrameControlValues
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::IReference<winrt::Windows::Foundation::TimeSpan>) Exposure() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::IReference<float>) ExposureCompensation() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::IReference<uint32_t>) IsoSpeed() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::IReference<uint32_t>) Focus() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::IReference<winrt::Windows::Media::Devices::CaptureSceneMode>) SceneMode() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::IReference<bool>) Flashed() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::IReference<float>) FlashPowerPercent() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::IReference<uint32_t>) WhiteBalance() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::IReference<float>) ZoomFactor() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::ICapturedFrameControlValues>
    {
        template <typename D> using type = consume_Windows_Media_Capture_ICapturedFrameControlValues<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_ICapturedFrameControlValues2
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::IReference<winrt::Windows::Media::Devices::MediaCaptureFocusState>) FocusState() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::IReference<double>) IsoDigitalGain() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::IReference<double>) IsoAnalogGain() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::MediaProperties::MediaRatio) SensorFrameRate() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::IReference<winrt::Windows::Media::Capture::WhiteBalanceGain>) WhiteBalanceGain() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::ICapturedFrameControlValues2>
    {
        template <typename D> using type = consume_Windows_Media_Capture_ICapturedFrameControlValues2<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_ICapturedFrameWithSoftwareBitmap
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Graphics::Imaging::SoftwareBitmap) SoftwareBitmap() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::ICapturedFrameWithSoftwareBitmap>
    {
        template <typename D> using type = consume_Windows_Media_Capture_ICapturedFrameWithSoftwareBitmap<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_ICapturedPhoto
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::CapturedFrame) Frame() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::CapturedFrame) Thumbnail() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::ICapturedPhoto>
    {
        template <typename D> using type = consume_Windows_Media_Capture_ICapturedPhoto<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_ILowLagMediaRecording
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) StartAsync() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) StopAsync() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) FinishAsync() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::ILowLagMediaRecording>
    {
        template <typename D> using type = consume_Windows_Media_Capture_ILowLagMediaRecording<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_ILowLagMediaRecording2
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) PauseAsync(winrt::Windows::Media::Devices::MediaCapturePauseBehavior const& behavior) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) ResumeAsync() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::ILowLagMediaRecording2>
    {
        template <typename D> using type = consume_Windows_Media_Capture_ILowLagMediaRecording2<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_ILowLagMediaRecording3
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::MediaCapturePauseResult>) PauseWithResultAsync(winrt::Windows::Media::Devices::MediaCapturePauseBehavior const& behavior) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::MediaCaptureStopResult>) StopWithResultAsync() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::ILowLagMediaRecording3>
    {
        template <typename D> using type = consume_Windows_Media_Capture_ILowLagMediaRecording3<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_ILowLagPhotoCapture
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::CapturedPhoto>) CaptureAsync() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) FinishAsync() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::ILowLagPhotoCapture>
    {
        template <typename D> using type = consume_Windows_Media_Capture_ILowLagPhotoCapture<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_ILowLagPhotoSequenceCapture
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) StartAsync() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) StopAsync() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) FinishAsync() const;
        WINRT_IMPL_AUTO(winrt::event_token) PhotoCaptured(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::LowLagPhotoSequenceCapture, winrt::Windows::Media::Capture::PhotoCapturedEventArgs> const& handler) const;
        using PhotoCaptured_revoker = impl::event_revoker<winrt::Windows::Media::Capture::ILowLagPhotoSequenceCapture, &impl::abi_t<winrt::Windows::Media::Capture::ILowLagPhotoSequenceCapture>::remove_PhotoCaptured>;
        [[nodiscard]] PhotoCaptured_revoker PhotoCaptured(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::LowLagPhotoSequenceCapture, winrt::Windows::Media::Capture::PhotoCapturedEventArgs> const& handler) const;
        WINRT_IMPL_AUTO(void) PhotoCaptured(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Windows::Media::Capture::ILowLagPhotoSequenceCapture>
    {
        template <typename D> using type = consume_Windows_Media_Capture_ILowLagPhotoSequenceCapture<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCapture
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) InitializeAsync() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) InitializeAsync(winrt::Windows::Media::Capture::MediaCaptureInitializationSettings const& mediaCaptureInitializationSettings) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) StartRecordToStorageFileAsync(winrt::Windows::Media::MediaProperties::MediaEncodingProfile const& encodingProfile, winrt::Windows::Storage::IStorageFile const& file) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) StartRecordToStreamAsync(winrt::Windows::Media::MediaProperties::MediaEncodingProfile const& encodingProfile, winrt::Windows::Storage::Streams::IRandomAccessStream const& stream) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) StartRecordToCustomSinkAsync(winrt::Windows::Media::MediaProperties::MediaEncodingProfile const& encodingProfile, winrt::Windows::Media::IMediaExtension const& customMediaSink) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) StartRecordToCustomSinkAsync(winrt::Windows::Media::MediaProperties::MediaEncodingProfile const& encodingProfile, param::hstring const& customSinkActivationId, winrt::Windows::Foundation::Collections::IPropertySet const& customSinkSettings) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) StopRecordAsync() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) CapturePhotoToStorageFileAsync(winrt::Windows::Media::MediaProperties::ImageEncodingProperties const& type, winrt::Windows::Storage::IStorageFile const& file) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) CapturePhotoToStreamAsync(winrt::Windows::Media::MediaProperties::ImageEncodingProperties const& type, winrt::Windows::Storage::Streams::IRandomAccessStream const& stream) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) AddEffectAsync(winrt::Windows::Media::Capture::MediaStreamType const& mediaStreamType, param::hstring const& effectActivationID, winrt::Windows::Foundation::Collections::IPropertySet const& effectSettings) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) ClearEffectsAsync(winrt::Windows::Media::Capture::MediaStreamType const& mediaStreamType) const;
        WINRT_IMPL_AUTO(void) SetEncoderProperty(winrt::Windows::Media::Capture::MediaStreamType const& mediaStreamType, winrt::guid const& propertyId, winrt::Windows::Foundation::IInspectable const& propertyValue) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IInspectable) GetEncoderProperty(winrt::Windows::Media::Capture::MediaStreamType const& mediaStreamType, winrt::guid const& propertyId) const;
        WINRT_IMPL_AUTO(winrt::event_token) Failed(winrt::Windows::Media::Capture::MediaCaptureFailedEventHandler const& errorEventHandler) const;
        using Failed_revoker = impl::event_revoker<winrt::Windows::Media::Capture::IMediaCapture, &impl::abi_t<winrt::Windows::Media::Capture::IMediaCapture>::remove_Failed>;
        [[nodiscard]] Failed_revoker Failed(auto_revoke_t, winrt::Windows::Media::Capture::MediaCaptureFailedEventHandler const& errorEventHandler) const;
        WINRT_IMPL_AUTO(void) Failed(winrt::event_token const& eventCookie) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) RecordLimitationExceeded(winrt::Windows::Media::Capture::RecordLimitationExceededEventHandler const& recordLimitationExceededEventHandler) const;
        using RecordLimitationExceeded_revoker = impl::event_revoker<winrt::Windows::Media::Capture::IMediaCapture, &impl::abi_t<winrt::Windows::Media::Capture::IMediaCapture>::remove_RecordLimitationExceeded>;
        [[nodiscard]] RecordLimitationExceeded_revoker RecordLimitationExceeded(auto_revoke_t, winrt::Windows::Media::Capture::RecordLimitationExceededEventHandler const& recordLimitationExceededEventHandler) const;
        WINRT_IMPL_AUTO(void) RecordLimitationExceeded(winrt::event_token const& eventCookie) const noexcept;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::MediaCaptureSettings) MediaCaptureSettings() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Devices::AudioDeviceController) AudioDeviceController() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Devices::VideoDeviceController) VideoDeviceController() const;
        WINRT_IMPL_AUTO(void) SetPreviewMirroring(bool value) const;
        WINRT_IMPL_AUTO(bool) GetPreviewMirroring() const;
        WINRT_IMPL_AUTO(void) SetPreviewRotation(winrt::Windows::Media::Capture::VideoRotation const& value) const;
        WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::VideoRotation) GetPreviewRotation() const;
        WINRT_IMPL_AUTO(void) SetRecordRotation(winrt::Windows::Media::Capture::VideoRotation const& value) const;
        WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::VideoRotation) GetRecordRotation() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCapture>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCapture<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCapture2
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::LowLagMediaRecording>) PrepareLowLagRecordToStorageFileAsync(winrt::Windows::Media::MediaProperties::MediaEncodingProfile const& encodingProfile, winrt::Windows::Storage::IStorageFile const& file) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::LowLagMediaRecording>) PrepareLowLagRecordToStreamAsync(winrt::Windows::Media::MediaProperties::MediaEncodingProfile const& encodingProfile, winrt::Windows::Storage::Streams::IRandomAccessStream const& stream) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::LowLagMediaRecording>) PrepareLowLagRecordToCustomSinkAsync(winrt::Windows::Media::MediaProperties::MediaEncodingProfile const& encodingProfile, winrt::Windows::Media::IMediaExtension const& customMediaSink) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::LowLagMediaRecording>) PrepareLowLagRecordToCustomSinkAsync(winrt::Windows::Media::MediaProperties::MediaEncodingProfile const& encodingProfile, param::hstring const& customSinkActivationId, winrt::Windows::Foundation::Collections::IPropertySet const& customSinkSettings) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::LowLagPhotoCapture>) PrepareLowLagPhotoCaptureAsync(winrt::Windows::Media::MediaProperties::ImageEncodingProperties const& type) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::LowLagPhotoSequenceCapture>) PrepareLowLagPhotoSequenceCaptureAsync(winrt::Windows::Media::MediaProperties::ImageEncodingProperties const& type) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) SetEncodingPropertiesAsync(winrt::Windows::Media::Capture::MediaStreamType const& mediaStreamType, winrt::Windows::Media::MediaProperties::IMediaEncodingProperties const& mediaEncodingProperties, winrt::Windows::Media::MediaProperties::MediaPropertySet const& encoderProperties) const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCapture2>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCapture2<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCapture3
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::Core::VariablePhotoSequenceCapture>) PrepareVariablePhotoSequenceCaptureAsync(winrt::Windows::Media::MediaProperties::ImageEncodingProperties const& type) const;
        WINRT_IMPL_AUTO(winrt::event_token) FocusChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Media::Capture::MediaCaptureFocusChangedEventArgs> const& handler) const;
        using FocusChanged_revoker = impl::event_revoker<winrt::Windows::Media::Capture::IMediaCapture3, &impl::abi_t<winrt::Windows::Media::Capture::IMediaCapture3>::remove_FocusChanged>;
        [[nodiscard]] FocusChanged_revoker FocusChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Media::Capture::MediaCaptureFocusChangedEventArgs> const& handler) const;
        WINRT_IMPL_AUTO(void) FocusChanged(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) PhotoConfirmationCaptured(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Media::Capture::PhotoConfirmationCapturedEventArgs> const& handler) const;
        using PhotoConfirmationCaptured_revoker = impl::event_revoker<winrt::Windows::Media::Capture::IMediaCapture3, &impl::abi_t<winrt::Windows::Media::Capture::IMediaCapture3>::remove_PhotoConfirmationCaptured>;
        [[nodiscard]] PhotoConfirmationCaptured_revoker PhotoConfirmationCaptured(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Media::Capture::PhotoConfirmationCapturedEventArgs> const& handler) const;
        WINRT_IMPL_AUTO(void) PhotoConfirmationCaptured(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCapture3>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCapture3<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCapture4
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::IMediaExtension>) AddAudioEffectAsync(winrt::Windows::Media::Effects::IAudioEffectDefinition const& definition) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::IMediaExtension>) AddVideoEffectAsync(winrt::Windows::Media::Effects::IVideoEffectDefinition const& definition, winrt::Windows::Media::Capture::MediaStreamType const& mediaStreamType) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) PauseRecordAsync(winrt::Windows::Media::Devices::MediaCapturePauseBehavior const& behavior) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) ResumeRecordAsync() const;
        WINRT_IMPL_AUTO(winrt::event_token) CameraStreamStateChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using CameraStreamStateChanged_revoker = impl::event_revoker<winrt::Windows::Media::Capture::IMediaCapture4, &impl::abi_t<winrt::Windows::Media::Capture::IMediaCapture4>::remove_CameraStreamStateChanged>;
        [[nodiscard]] CameraStreamStateChanged_revoker CameraStreamStateChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Foundation::IInspectable> const& handler) const;
        WINRT_IMPL_AUTO(void) CameraStreamStateChanged(winrt::event_token const& token) const noexcept;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Devices::CameraStreamState) CameraStreamState() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::VideoFrame>) GetPreviewFrameAsync() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::VideoFrame>) GetPreviewFrameAsync(winrt::Windows::Media::VideoFrame const& destination) const;
        WINRT_IMPL_AUTO(winrt::event_token) ThermalStatusChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using ThermalStatusChanged_revoker = impl::event_revoker<winrt::Windows::Media::Capture::IMediaCapture4, &impl::abi_t<winrt::Windows::Media::Capture::IMediaCapture4>::remove_ThermalStatusChanged>;
        [[nodiscard]] ThermalStatusChanged_revoker ThermalStatusChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Foundation::IInspectable> const& handler) const;
        WINRT_IMPL_AUTO(void) ThermalStatusChanged(winrt::event_token const& token) const noexcept;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::MediaCaptureThermalStatus) ThermalStatus() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::AdvancedPhotoCapture>) PrepareAdvancedPhotoCaptureAsync(winrt::Windows::Media::MediaProperties::ImageEncodingProperties const& encodingProperties) const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCapture4>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCapture4<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCapture5
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) RemoveEffectAsync(winrt::Windows::Media::IMediaExtension const& effect) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::MediaCapturePauseResult>) PauseRecordWithResultAsync(winrt::Windows::Media::Devices::MediaCapturePauseBehavior const& behavior) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::MediaCaptureStopResult>) StopRecordWithResultAsync() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::Collections::IMapView<hstring, winrt::Windows::Media::Capture::Frames::MediaFrameSource>) FrameSources() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::Frames::MediaFrameReader>) CreateFrameReaderAsync(winrt::Windows::Media::Capture::Frames::MediaFrameSource const& inputSource) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::Frames::MediaFrameReader>) CreateFrameReaderAsync(winrt::Windows::Media::Capture::Frames::MediaFrameSource const& inputSource, param::hstring const& outputSubtype) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::Frames::MediaFrameReader>) CreateFrameReaderAsync(winrt::Windows::Media::Capture::Frames::MediaFrameSource const& inputSource, param::hstring const& outputSubtype, winrt::Windows::Graphics::Imaging::BitmapSize const& outputSize) const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCapture5>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCapture5<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCapture6
    {
        WINRT_IMPL_AUTO(winrt::event_token) CaptureDeviceExclusiveControlStatusChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Media::Capture::MediaCaptureDeviceExclusiveControlStatusChangedEventArgs> const& handler) const;
        using CaptureDeviceExclusiveControlStatusChanged_revoker = impl::event_revoker<winrt::Windows::Media::Capture::IMediaCapture6, &impl::abi_t<winrt::Windows::Media::Capture::IMediaCapture6>::remove_CaptureDeviceExclusiveControlStatusChanged>;
        [[nodiscard]] CaptureDeviceExclusiveControlStatusChanged_revoker CaptureDeviceExclusiveControlStatusChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Media::Capture::MediaCaptureDeviceExclusiveControlStatusChangedEventArgs> const& handler) const;
        WINRT_IMPL_AUTO(void) CaptureDeviceExclusiveControlStatusChanged(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::Frames::MultiSourceMediaFrameReader>) CreateMultiSourceFrameReaderAsync(param::async_iterable<winrt::Windows::Media::Capture::Frames::MediaFrameSource> const& inputSources) const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCapture6>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCapture6<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCapture7
    {
        WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::MediaCaptureRelativePanelWatcher) CreateRelativePanelWatcher(winrt::Windows::Media::Capture::StreamingCaptureMode const& captureMode, winrt::Windows::UI::WindowManagement::DisplayRegion const& displayRegion) const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCapture7>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCapture7<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCaptureDeviceExclusiveControlStatusChangedEventArgs
    {
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) DeviceId() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::MediaCaptureDeviceExclusiveControlStatus) Status() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCaptureDeviceExclusiveControlStatusChangedEventArgs>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCaptureDeviceExclusiveControlStatusChangedEventArgs<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCaptureFailedEventArgs
    {
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) Message() const;
        [[nodiscard]] WINRT_IMPL_AUTO(uint32_t) Code() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCaptureFailedEventArgs>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCaptureFailedEventArgs<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCaptureFocusChangedEventArgs
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Devices::MediaCaptureFocusState) FocusState() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCaptureFocusChangedEventArgs>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCaptureFocusChangedEventArgs<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCaptureInitializationSettings
    {
        WINRT_IMPL_AUTO(void) AudioDeviceId(param::hstring const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) AudioDeviceId() const;
        WINRT_IMPL_AUTO(void) VideoDeviceId(param::hstring const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) VideoDeviceId() const;
        WINRT_IMPL_AUTO(void) StreamingCaptureMode(winrt::Windows::Media::Capture::StreamingCaptureMode const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::StreamingCaptureMode) StreamingCaptureMode() const;
        WINRT_IMPL_AUTO(void) PhotoCaptureSource(winrt::Windows::Media::Capture::PhotoCaptureSource const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::PhotoCaptureSource) PhotoCaptureSource() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCaptureInitializationSettings<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCaptureInitializationSettings2
    {
        WINRT_IMPL_AUTO(void) MediaCategory(winrt::Windows::Media::Capture::MediaCategory const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::MediaCategory) MediaCategory() const;
        WINRT_IMPL_AUTO(void) AudioProcessing(winrt::Windows::Media::AudioProcessing const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::AudioProcessing) AudioProcessing() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings2>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCaptureInitializationSettings2<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCaptureInitializationSettings3
    {
        WINRT_IMPL_AUTO(void) AudioSource(winrt::Windows::Media::Core::IMediaSource const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Core::IMediaSource) AudioSource() const;
        WINRT_IMPL_AUTO(void) VideoSource(winrt::Windows::Media::Core::IMediaSource const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Core::IMediaSource) VideoSource() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings3>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCaptureInitializationSettings3<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCaptureInitializationSettings4
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::MediaCaptureVideoProfile) VideoProfile() const;
        WINRT_IMPL_AUTO(void) VideoProfile(winrt::Windows::Media::Capture::MediaCaptureVideoProfile const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription) PreviewMediaDescription() const;
        WINRT_IMPL_AUTO(void) PreviewMediaDescription(winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription) RecordMediaDescription() const;
        WINRT_IMPL_AUTO(void) RecordMediaDescription(winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription) PhotoMediaDescription() const;
        WINRT_IMPL_AUTO(void) PhotoMediaDescription(winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription const& value) const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings4>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCaptureInitializationSettings4<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCaptureInitializationSettings5
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::Frames::MediaFrameSourceGroup) SourceGroup() const;
        WINRT_IMPL_AUTO(void) SourceGroup(winrt::Windows::Media::Capture::Frames::MediaFrameSourceGroup const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::MediaCaptureSharingMode) SharingMode() const;
        WINRT_IMPL_AUTO(void) SharingMode(winrt::Windows::Media::Capture::MediaCaptureSharingMode const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::MediaCaptureMemoryPreference) MemoryPreference() const;
        WINRT_IMPL_AUTO(void) MemoryPreference(winrt::Windows::Media::Capture::MediaCaptureMemoryPreference const& value) const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings5>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCaptureInitializationSettings5<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCaptureInitializationSettings6
    {
        [[nodiscard]] WINRT_IMPL_AUTO(bool) AlwaysPlaySystemShutterSound() const;
        WINRT_IMPL_AUTO(void) AlwaysPlaySystemShutterSound(bool value) const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings6>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCaptureInitializationSettings6<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCaptureInitializationSettings7
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Security::Credentials::PasswordCredential) DeviceUriPasswordCredential() const;
        WINRT_IMPL_AUTO(void) DeviceUriPasswordCredential(winrt::Windows::Security::Credentials::PasswordCredential const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::Uri) DeviceUri() const;
        WINRT_IMPL_AUTO(void) DeviceUri(winrt::Windows::Foundation::Uri const& value) const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings7>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCaptureInitializationSettings7<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCapturePauseResult
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::VideoFrame) LastFrame() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::TimeSpan) RecordDuration() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCapturePauseResult>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCapturePauseResult<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCaptureRelativePanelWatcher
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Devices::Enumeration::Panel) RelativePanel() const;
        WINRT_IMPL_AUTO(winrt::event_token) Changed(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCaptureRelativePanelWatcher, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using Changed_revoker = impl::event_revoker<winrt::Windows::Media::Capture::IMediaCaptureRelativePanelWatcher, &impl::abi_t<winrt::Windows::Media::Capture::IMediaCaptureRelativePanelWatcher>::remove_Changed>;
        [[nodiscard]] Changed_revoker Changed(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCaptureRelativePanelWatcher, winrt::Windows::Foundation::IInspectable> const& handler) const;
        WINRT_IMPL_AUTO(void) Changed(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(void) Start() const;
        WINRT_IMPL_AUTO(void) Stop() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCaptureRelativePanelWatcher>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCaptureRelativePanelWatcher<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCaptureSettings
    {
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) AudioDeviceId() const;
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) VideoDeviceId() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::StreamingCaptureMode) StreamingCaptureMode() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::PhotoCaptureSource) PhotoCaptureSource() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::VideoDeviceCharacteristic) VideoDeviceCharacteristic() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCaptureSettings>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCaptureSettings<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCaptureSettings2
    {
        [[nodiscard]] WINRT_IMPL_AUTO(bool) ConcurrentRecordAndPhotoSupported() const;
        [[nodiscard]] WINRT_IMPL_AUTO(bool) ConcurrentRecordAndPhotoSequenceSupported() const;
        [[nodiscard]] WINRT_IMPL_AUTO(bool) CameraSoundRequiredForRegion() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::IReference<uint32_t>) Horizontal35mmEquivalentFocalLength() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::IReference<int32_t>) PitchOffsetDegrees() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::IReference<uint32_t>) Vertical35mmEquivalentFocalLength() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::MediaCategory) MediaCategory() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::AudioProcessing) AudioProcessing() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCaptureSettings2>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCaptureSettings2<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCaptureSettings3
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Graphics::DirectX::Direct3D11::IDirect3DDevice) Direct3D11Device() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCaptureSettings3>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCaptureSettings3<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCaptureStatics
    {
        WINRT_IMPL_AUTO(bool) IsVideoProfileSupported(param::hstring const& videoDeviceId) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::MediaCaptureVideoProfile>) FindAllVideoProfiles(param::hstring const& videoDeviceId) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::MediaCaptureVideoProfile>) FindConcurrentProfiles(param::hstring const& videoDeviceId) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::MediaCaptureVideoProfile>) FindKnownVideoProfiles(param::hstring const& videoDeviceId, winrt::Windows::Media::Capture::KnownVideoProfile const& name) const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCaptureStatics>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCaptureStatics<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCaptureStopResult
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::VideoFrame) LastFrame() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::TimeSpan) RecordDuration() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCaptureStopResult>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCaptureStopResult<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCaptureVideoPreview
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) StartPreviewAsync() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) StartPreviewToCustomSinkAsync(winrt::Windows::Media::MediaProperties::MediaEncodingProfile const& encodingProfile, winrt::Windows::Media::IMediaExtension const& customMediaSink) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) StartPreviewToCustomSinkAsync(winrt::Windows::Media::MediaProperties::MediaEncodingProfile const& encodingProfile, param::hstring const& customSinkActivationId, winrt::Windows::Foundation::Collections::IPropertySet const& customSinkSettings) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncAction) StopPreviewAsync() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCaptureVideoPreview>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCaptureVideoPreview<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCaptureVideoProfile
    {
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) Id() const;
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) VideoDeviceId() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription>) SupportedPreviewMediaDescription() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription>) SupportedRecordMediaDescription() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription>) SupportedPhotoMediaDescription() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::MediaCaptureVideoProfile>) GetConcurrency() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCaptureVideoProfile>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCaptureVideoProfile<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCaptureVideoProfile2
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::Frames::MediaFrameSourceInfo>) FrameSourceInfos() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::Collections::IMapView<winrt::guid, winrt::Windows::Foundation::IInspectable>) Properties() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCaptureVideoProfile2>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCaptureVideoProfile2<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCaptureVideoProfileMediaDescription
    {
        [[nodiscard]] WINRT_IMPL_AUTO(uint32_t) Width() const;
        [[nodiscard]] WINRT_IMPL_AUTO(uint32_t) Height() const;
        [[nodiscard]] WINRT_IMPL_AUTO(double) FrameRate() const;
        [[nodiscard]] WINRT_IMPL_AUTO(bool) IsVariablePhotoSequenceSupported() const;
        [[nodiscard]] WINRT_IMPL_AUTO(bool) IsHdrVideoSupported() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCaptureVideoProfileMediaDescription<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IMediaCaptureVideoProfileMediaDescription2
    {
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) Subtype() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::Collections::IMapView<winrt::guid, winrt::Windows::Foundation::IInspectable>) Properties() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription2>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IMediaCaptureVideoProfileMediaDescription2<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IOptionalReferencePhotoCapturedEventArgs
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::CapturedFrame) Frame() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::IInspectable) Context() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IOptionalReferencePhotoCapturedEventArgs>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IOptionalReferencePhotoCapturedEventArgs<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IPhotoCapturedEventArgs
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::CapturedFrame) Frame() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::CapturedFrame) Thumbnail() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::TimeSpan) CaptureTimeOffset() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IPhotoCapturedEventArgs>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IPhotoCapturedEventArgs<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IPhotoConfirmationCapturedEventArgs
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::Capture::CapturedFrame) Frame() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Foundation::TimeSpan) CaptureTimeOffset() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IPhotoConfirmationCapturedEventArgs>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IPhotoConfirmationCapturedEventArgs<D>;
    };
    template <typename D>
    struct consume_Windows_Media_Capture_IVideoStreamConfiguration
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::MediaProperties::VideoEncodingProperties) InputProperties() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::Media::MediaProperties::VideoEncodingProperties) OutputProperties() const;
    };
    template <> struct consume<winrt::Windows::Media::Capture::IVideoStreamConfiguration>
    {
        template <typename D> using type = consume_Windows_Media_Capture_IVideoStreamConfiguration<D>;
    };
    struct struct_Windows_Media_Capture_WhiteBalanceGain
    {
        double R;
        double G;
        double B;
    };
    template <> struct abi<Windows::Media::Capture::WhiteBalanceGain>
    {
        using type = struct_Windows_Media_Capture_WhiteBalanceGain;
    };
}
#endif
