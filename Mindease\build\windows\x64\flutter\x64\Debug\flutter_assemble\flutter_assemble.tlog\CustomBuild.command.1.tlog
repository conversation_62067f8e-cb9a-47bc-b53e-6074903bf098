^C:\USERS\<USER>\DOWNLOADS\MINDEASE\MINDEASE\BUILD\WINDOWS\X64\CMAKEFILES\91269E516C0BD1AEC47580DD200A7A9A\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\flutter PROJECT_DIR=C:\Users\<USER>\Downloads\Mindease\Mindease FLUTTER_ROOT=C:\flutter FLUTTER_EPHEMERAL_DIR=C:\Users\<USER>\Downloads\Mindease\Mindease\windows\flutter\ephemeral PROJECT_DIR=C:\Users\<USER>\Downloads\Mindease\Mindease FLUTTER_TARGET=C:\Users\<USER>\Downloads\Mindease\Mindease\lib\main.dart DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=C:\Users\<USER>\Downloads\Mindease\Mindease\.dart_tool\package_config.json C:/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\MINDEASE\MINDEASE\BUILD\WINDOWS\X64\CMAKEFILES\ECFF572DFCA42095E2C4D1E36A164513\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOWNLOADS\MINDEASE\MINDEASE\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Downloads/Mindease/Mindease/windows -BC:/Users/<USER>/Downloads/Mindease/Mindease/build/windows/x64 --check-stamp-file C:/Users/<USER>/Downloads/Mindease/Mindease/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
